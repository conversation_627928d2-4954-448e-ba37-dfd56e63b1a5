using UnityEngine;
using System.Collections;

namespace Menu
{
    /// <summary>
    /// Component để di chuyển clouds trong background
    /// </summary>
    public class CloudMover : MonoBehaviour
    {
        [Header("Movement Settings")]
        [SerializeField] private float moveSpeed = 10f;
        [SerializeField] private float speedMultiplier = 1f;
        [SerializeField] private Vector2 direction = Vector2.right;
        
        [Header("Boundaries")]
        [SerializeField] private float resetPositionX = -200f;
        [SerializeField] private float startPositionX = 2000f;
        
        [Header("Floating Animation")]
        [SerializeField] private bool enableFloating = true;
        [SerializeField] private float floatAmplitude = 10f;
        [SerializeField] private float floatFrequency = 0.5f;
        
        private RectTransform rectTransform;
        private Vector2 originalPosition;
        private float floatOffset;
        private bool isMoving = true;
        
        /// <summary>
        /// Khởi tạo cloud mover với tham số
        /// </summary>
        public void Initialize(float speed, float multiplier)
        {
            moveSpeed = speed;
            speedMultiplier = multiplier;
            Setup();
        }
        
        private void Start()
        {
            Setup();
        }
        
        private void Setup()
        {
            rectTransform = GetComponent<RectTransform>();
            if (rectTransform != null)
            {
                originalPosition = rectTransform.anchoredPosition;
                floatOffset = Random.Range(0f, Mathf.PI * 2f); // Random phase cho floating
            }
        }
        
        private void Update()
        {
            if (isMoving && rectTransform != null)
            {
                MoveCloud();
                
                if (enableFloating)
                {
                    ApplyFloatingMotion();
                }
                
                CheckBoundaries();
            }
        }
        
        /// <summary>
        /// Di chuyển cloud theo hướng đã định
        /// </summary>
        private void MoveCloud()
        {
            Vector2 currentPosition = rectTransform.anchoredPosition;
            Vector2 movement = direction.normalized * moveSpeed * speedMultiplier * Time.deltaTime;
            
            currentPosition += movement;
            rectTransform.anchoredPosition = currentPosition;
        }
        
        /// <summary>
        /// Áp dụng chuyển động floating lên xuống
        /// </summary>
        private void ApplyFloatingMotion()
        {
            float floatY = Mathf.Sin((Time.time + floatOffset) * floatFrequency) * floatAmplitude;
            
            Vector2 currentPosition = rectTransform.anchoredPosition;
            currentPosition.y = originalPosition.y + floatY;
            rectTransform.anchoredPosition = currentPosition;
        }
        
        /// <summary>
        /// Kiểm tra boundaries và reset position nếu cần
        /// </summary>
        private void CheckBoundaries()
        {
            Vector2 currentPosition = rectTransform.anchoredPosition;
            
            // Nếu cloud đi ra khỏi màn hình bên trái, reset về bên phải
            if (currentPosition.x < resetPositionX)
            {
                currentPosition.x = startPositionX;
                currentPosition.y = GetRandomYPosition();
                rectTransform.anchoredPosition = currentPosition;
                originalPosition = currentPosition;
                
                // Random lại speed để tạo sự đa dạng
                speedMultiplier = Random.Range(0.5f, 1.5f);
            }
        }
        
        /// <summary>
        /// Lấy random Y position cho cloud
        /// </summary>
        private float GetRandomYPosition()
        {
            return Random.Range(Screen.height * 0.6f, Screen.height * 0.9f);
        }
        
        /// <summary>
        /// Bắt đầu di chuyển
        /// </summary>
        public void StartMoving()
        {
            isMoving = true;
        }
        
        /// <summary>
        /// Dừng di chuyển
        /// </summary>
        public void StopMoving()
        {
            isMoving = false;
        }
        
        /// <summary>
        /// Thay đổi tốc độ di chuyển
        /// </summary>
        public void SetMoveSpeed(float newSpeed)
        {
            moveSpeed = newSpeed;
        }
        
        /// <summary>
        /// Thay đổi hướng di chuyển
        /// </summary>
        public void SetDirection(Vector2 newDirection)
        {
            direction = newDirection.normalized;
        }
        
        /// <summary>
        /// Reset cloud về vị trí ban đầu
        /// </summary>
        public void ResetPosition()
        {
            if (rectTransform != null)
            {
                Vector2 newPosition = new Vector2(
                    Random.Range(startPositionX * 0.5f, startPositionX),
                    GetRandomYPosition()
                );
                
                rectTransform.anchoredPosition = newPosition;
                originalPosition = newPosition;
            }
        }
        
        /// <summary>
        /// Tạo hiệu ứng gió mạnh (tăng tốc độ tạm thời)
        /// </summary>
        public void ApplyWindEffect(float windStrength, float duration)
        {
            StartCoroutine(WindEffectCoroutine(windStrength, duration));
        }
        
        /// <summary>
        /// Coroutine cho hiệu ứng gió
        /// </summary>
        private IEnumerator WindEffectCoroutine(float windStrength, float duration)
        {
            float originalMultiplier = speedMultiplier;
            speedMultiplier *= windStrength;
            
            yield return new WaitForSeconds(duration);
            
            speedMultiplier = originalMultiplier;
        }
        
        /// <summary>
        /// Tạo hiệu ứng fade in khi cloud xuất hiện
        /// </summary>
        public void FadeIn(float duration = 1f)
        {
            StartCoroutine(FadeInCoroutine(duration));
        }
        
        /// <summary>
        /// Coroutine fade in
        /// </summary>
        private IEnumerator FadeInCoroutine(float duration)
        {
            UnityEngine.UI.Image cloudImage = GetComponent<UnityEngine.UI.Image>();
            if (cloudImage == null) yield break;
            
            Color startColor = cloudImage.color;
            startColor.a = 0f;
            cloudImage.color = startColor;
            
            Color endColor = startColor;
            endColor.a = 1f;
            
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float progress = elapsed / duration;
                
                Color currentColor = Color.Lerp(startColor, endColor, progress);
                cloudImage.color = currentColor;
                
                yield return null;
            }
            
            cloudImage.color = endColor;
        }
        
        /// <summary>
        /// Pause/Resume movement
        /// </summary>
        public void SetPaused(bool paused)
        {
            isMoving = !paused;
        }
        
        private void OnEnable()
        {
            StartMoving();
        }
        
        private void OnDisable()
        {
            StopMoving();
        }
    }
}
