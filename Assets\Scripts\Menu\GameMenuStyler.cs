using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace Menu
{
    /// <summary>
    /// Tạo menu game đẹp chỉ với assets có sẵn
    /// </summary>
    public class GameMenuStyler : MonoBehaviour
    {
        [Header("Settings")]
        [SerializeField] private bool applyOnStart = true;
        
        [Header("Colors - Game Style")]
        [SerializeField] private Color panelColor = new Color(0.8f, 0.6f, 0.2f, 0.9f); // Vàng đồng
        [SerializeField] private Color buttonEasyColor = new Color(1f, 0.9f, 0.3f, 1f); // Vàng sáng
        [SerializeField] private Color buttonMediumColor = new Color(1f, 0.7f, 0.2f, 1f); // Cam vàng
        [SerializeField] private Color buttonHardColor = new Color(1f, 0.4f, 0.1f, 1f); // Cam đỏ
        [SerializeField] private Color textColor = new Color(0.2f, 0.1f, 0f, 1f); // Nâ<PERSON> đậm
        [SerializeField] private Color titleColor = Color.white;
        
        private void Start()
        {
            if (applyOnStart)
            {
                ApplyGameMenuStyle();
            }
        }
        
        /// <summary>
        /// Áp dụng style menu game với assets có sẵn
        /// </summary>
        [ContextMenu("Apply Game Menu Style")]
        public void ApplyGameMenuStyle()
        {
            Debug.Log("GameMenuStyler: Applying game menu style with existing assets...");
            
            // 1. Setup Canvas và scale
            SetupCanvas();
            
            // 2. Style background panel
            CreateGamePanel();
            
            // 3. Style buttons với gradient colors
            StyleGameButtons();
            
            // 4. Style text với Gixel font
            StyleGameTexts();
            
            // 5. Add background music
            AddBackgroundMusic();
            
            Debug.Log("GameMenuStyler: Game menu style applied!");
        }
        
        /// <summary>
        /// Setup Canvas scale
        /// </summary>
        private void SetupCanvas()
        {
            Canvas canvas = FindObjectOfType<Canvas>();
            if (canvas != null)
            {
                CanvasScaler scaler = canvas.GetComponent<CanvasScaler>();
                if (scaler != null)
                {
                    scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
                    scaler.referenceResolution = new Vector2(1920, 1080);
                    scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
                    scaler.matchWidthOrHeight = 0.5f;
                }
            }
        }
        
        /// <summary>
        /// Tạo panel game với Box sprite có sẵn
        /// </summary>
        private void CreateGamePanel()
        {
            Canvas canvas = FindObjectOfType<Canvas>();
            if (canvas == null) return;
            
            // Tìm hoặc tạo main panel
            GameObject panel = GameObject.Find("GameMenuPanel");
            if (panel == null)
            {
                panel = new GameObject("GameMenuPanel");
                panel.transform.SetParent(canvas.transform, false);
                panel.transform.SetAsFirstSibling();
            }
            
            // Setup RectTransform
            RectTransform panelRect = panel.GetComponent<RectTransform>();
            if (panelRect == null)
                panelRect = panel.AddComponent<RectTransform>();
            
            // Center panel
            panelRect.anchorMin = new Vector2(0.5f, 0.5f);
            panelRect.anchorMax = new Vector2(0.5f, 0.5f);
            panelRect.anchoredPosition = Vector2.zero;
            panelRect.sizeDelta = new Vector2(600, 500);
            
            // Add Image với Box sprite có sẵn
            Image panelImage = panel.GetComponent<Image>();
            if (panelImage == null)
                panelImage = panel.AddComponent<Image>();
            
            // Load Box sprite
            Sprite boxSprite = Resources.Load<Sprite>("Sprites/UI/BoxWhiteOutline");
            if (boxSprite == null)
                boxSprite = Resources.Load<Sprite>("UI/BoxWhiteOutline");
            
            if (boxSprite != null)
            {
                panelImage.sprite = boxSprite;
                panelImage.type = Image.Type.Sliced;
                panelImage.color = panelColor;
            }
            else
            {
                // Fallback: solid color
                panelImage.color = panelColor;
            }
        }
        
        /// <summary>
        /// Style buttons giống game với gradient colors
        /// </summary>
        private void StyleGameButtons()
        {
            Button[] buttons = FindObjectsOfType<Button>();
            
            for (int i = 0; i < buttons.Length; i++)
            {
                Button button = buttons[i];
                StyleSingleButton(button, i);
            }
        }
        
        /// <summary>
        /// Style một button với gradient color
        /// </summary>
        private void StyleSingleButton(Button button, int index)
        {
            // Load button sprite
            Sprite buttonSprite = Resources.Load<Sprite>("Sprites/UI/Box");
            if (buttonSprite == null)
                buttonSprite = Resources.Load<Sprite>("UI/Box");
            
            Image buttonImage = button.GetComponent<Image>();
            if (buttonImage != null)
            {
                if (buttonSprite != null)
                {
                    buttonImage.sprite = buttonSprite;
                    buttonImage.type = Image.Type.Sliced;
                }
                
                // Assign gradient colors based on button order
                Color buttonColor = GetButtonColor(index);
                Color hoverColor = Color.Lerp(buttonColor, Color.white, 0.2f);
                Color pressColor = Color.Lerp(buttonColor, Color.black, 0.2f);
                
                ColorBlock colors = button.colors;
                colors.normalColor = buttonColor;
                colors.highlightedColor = hoverColor;
                colors.pressedColor = pressColor;
                colors.selectedColor = hoverColor;
                colors.fadeDuration = 0.1f;
                button.colors = colors;
            }
            
            // Resize button
            RectTransform buttonRect = button.GetComponent<RectTransform>();
            if (buttonRect != null)
            {
                buttonRect.sizeDelta = new Vector2(300, 60);
            }
        }
        
        /// <summary>
        /// Get button color based on index (Easy, Medium, Hard style)
        /// </summary>
        private Color GetButtonColor(int index)
        {
            switch (index % 3)
            {
                case 0: return buttonEasyColor;   // Vàng sáng
                case 1: return buttonMediumColor; // Cam vàng  
                case 2: return buttonHardColor;   // Cam đỏ
                default: return buttonEasyColor;
            }
        }
        
        /// <summary>
        /// Style text với Gixel font có sẵn
        /// </summary>
        private void StyleGameTexts()
        {
            // Load Gixel font
            TMP_FontAsset gixelFont = Resources.Load<TMP_FontAsset>("Sprites/UI/Gixel SDF");
            if (gixelFont == null)
                gixelFont = Resources.Load<TMP_FontAsset>("UI/Gixel SDF");
            if (gixelFont == null)
                gixelFont = Resources.Load<TMP_FontAsset>("Gixel SDF");
            
            TextMeshProUGUI[] texts = FindObjectsOfType<TextMeshProUGUI>();
            
            foreach (TextMeshProUGUI text in texts)
            {
                // Apply Gixel font if available
                if (gixelFont != null)
                    text.font = gixelFont;
                
                // Style based on text content/name
                bool isTitle = text.name.ToLower().Contains("title") || 
                              text.text.ToLower().Contains("menu") ||
                              text.fontSize > 30;
                
                if (isTitle)
                {
                    text.fontSize = 36;
                    text.color = titleColor;
                    text.fontStyle = FontStyles.Bold;
                    
                    // Add outline for title
                    text.outlineWidth = 0.2f;
                    text.outlineColor = Color.black;
                }
                else
                {
                    text.fontSize = 24;
                    text.color = textColor;
                    text.fontStyle = FontStyles.Bold;
                    
                    // Add subtle outline
                    text.outlineWidth = 0.1f;
                    text.outlineColor = new Color(0, 0, 0, 0.5f);
                }
                
                // Center alignment
                text.alignment = TextAlignmentOptions.Center;
            }
        }
        
        /// <summary>
        /// Add background music từ assets có sẵn
        /// </summary>
        private void AddBackgroundMusic()
        {
            AudioSource audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();
            
            // Load background music từ 8Bit Music folder
            AudioClip bgMusic = Resources.Load<AudioClip>("8Bit Music - 062022/1. Track 1");
            if (bgMusic == null)
            {
                // Try alternative paths
                bgMusic = Resources.Load<AudioClip>("1. Track 1");
            }
            
            if (bgMusic != null)
            {
                audioSource.clip = bgMusic;
                audioSource.loop = true;
                audioSource.volume = 0.3f;
                audioSource.playOnAwake = false;
                
                if (!audioSource.isPlaying)
                    audioSource.Play();
                
                Debug.Log("GameMenuStyler: Background music added!");
            }
            else
            {
                Debug.LogWarning("GameMenuStyler: Could not find background music in Resources.");
            }
        }
        
        /// <summary>
        /// Reset về trạng thái ban đầu
        /// </summary>
        [ContextMenu("Reset Menu Style")]
        public void ResetMenuStyle()
        {
            // Stop music
            AudioSource audioSource = GetComponent<AudioSource>();
            if (audioSource != null && audioSource.isPlaying)
                audioSource.Stop();
            
            // Remove game panel
            GameObject panel = GameObject.Find("GameMenuPanel");
            if (panel != null)
                DestroyImmediate(panel);
            
            Debug.Log("GameMenuStyler: Menu style reset!");
        }
    }
}
