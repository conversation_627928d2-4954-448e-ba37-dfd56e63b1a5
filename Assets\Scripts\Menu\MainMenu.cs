using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Menu
{
    public class MainMenu : MonoBehaviour
    {
        [Header("UI Scaling Settings")]
        [SerializeField] private float uiScaleFactor = 0.6f; // Giảm kích thước UI xuống 60%
        [SerializeField] private bool autoScaleOnStart = true;

        private void Start()
        {
            if (autoScaleOnStart)
            {
                ScaleMainMenuUI();
            }
        }

        /// <summary>
        /// Điều chỉnh kích thước của tất cả UI elements trong MainMenu
        /// </summary>
        private void ScaleMainMenuUI()
        {
            // Tìm tất cả các Canvas trong scene
            Canvas[] canvases = FindObjectsOfType<Canvas>();

            foreach (Canvas canvas in canvases)
            {
                // Chỉ điều chỉnh Canvas có CanvasScaler
                CanvasScaler scaler = canvas.GetComponent<CanvasScaler>();
                if (scaler != null)
                {
                    // Điều chỉnh reference resolution để UI nhỏ hơn
                    Vector2 currentResolution = scaler.referenceResolution;
                    scaler.referenceResolution = new Vector2(
                        currentResolution.x / uiScaleFactor,
                        currentResolution.y / uiScaleFactor
                    );

                    Debug.Log($"MainMenu: Scaled Canvas '{canvas.name}' resolution from {currentResolution} to {scaler.referenceResolution}");
                }

                // Điều chỉnh các elements có scale quá lớn
                ScaleUIElements(canvas.transform);
            }
        }

        /// <summary>
        /// Đệ quy điều chỉnh scale của các UI elements
        /// </summary>
        private void ScaleUIElements(Transform parent)
        {
            foreach (Transform child in parent)
            {
                // Kiểm tra nếu scale quá lớn (> 2.0) thì giảm xuống
                if (child.localScale.x > 2.0f || child.localScale.y > 2.0f)
                {
                    Vector3 newScale = child.localScale * uiScaleFactor;
                    child.localScale = newScale;

                    Debug.Log($"MainMenu: Scaled '{child.name}' from original scale to {newScale}");
                }

                // Đệ quy cho các child elements
                if (child.childCount > 0)
                {
                    ScaleUIElements(child);
                }
            }
        }

        /// <summary>
        /// Method công khai để điều chỉnh lại UI scale (có thể gọi từ Inspector hoặc code khác)
        /// </summary>
        [ContextMenu("Rescale Main Menu UI")]
        public void RescaleUI()
        {
            ScaleMainMenuUI();
        }

        public void Play()
        {
            SceneManager.LoadScene("Scene1");
        }

        public void Quit()
        {
            Debug.Log("Quit button pressed!");

            #if UNITY_EDITOR
                EditorApplication.isPlaying = false;
            #else
                Application.Quit();
            #endif
        }
    }
}
