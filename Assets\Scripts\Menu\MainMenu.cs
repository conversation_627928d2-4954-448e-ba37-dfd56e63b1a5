using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using TMPro;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Menu
{
    /// <summary>
    /// MainMenu với style pixel game giống hình tham khảo
    /// G<PERSON><PERSON> nguyên nội dung menu gốc (Play, Quit)
    /// </summary>
    public class MainMenu : MonoBehaviour
    {
        [Header("Pixel Game Menu Style")]
        [SerializeField] private bool applyStyleOnStart = true;

        // Colors giống hình tham khảo - background tím, chữ vàng
        private readonly Color backgroundColor = new Color(0.25f, 0.15f, 0.4f, 1f); // Tím đậm giống hình
        private readonly Color titleColor = new Color(1f, 0.7f, 0.1f, 1f); // Vàng cam cho "START GAME"
        private readonly Color menuTextColor = new Color(1f, 0.85f, 0.2f, 1f); // Vàng sáng cho menu items
        private readonly Color selectedColor = Color.white; // Trắng khi hover
        private readonly Color starColor = Color.white; // Màu sao trắng

        private void Start()
        {
            if (applyStyleOnStart)
            {
                ApplyPixelGameStyle();
            }
        }

        /// <summary>
        /// Apply pixel game style giống hình tham khảo
        /// </summary>
        [ContextMenu("Apply Pixel Game Style")]
        public void ApplyPixelGameStyle()
        {
            Debug.Log("Applying pixel game style like reference image...");

            CreatePixelGameMenu();
            CreateStarBackground();
            AddBackgroundMusic();

            Debug.Log("Pixel game style applied!");
        }

        /// <summary>
        /// Tạo menu pixel game với layout giống hình
        /// </summary>
        private void CreatePixelGameMenu()
        {
            // Setup Canvas
            Canvas canvas = FindObjectOfType<Canvas>();
            if (canvas == null)
            {
                GameObject canvasObj = new GameObject("Canvas");
                canvas = canvasObj.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvasObj.AddComponent<CanvasScaler>();
                canvasObj.AddComponent<GraphicRaycaster>();
            }

            // Setup Canvas Scaler cho pixel perfect
            CanvasScaler scaler = canvas.GetComponent<CanvasScaler>();
            scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            scaler.referenceResolution = new Vector2(1920, 1080);
            scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
            scaler.matchWidthOrHeight = 0.5f;

            // Tạo background tím
            CreateBackground(canvas);

            // Tạo title "START GAME" giống hình
            CreateTitle(canvas);

            // Tạo menu items (Play, Quit) với style pixel
            CreateMenuItems(canvas);
        }

        /// <summary>
        /// Tạo background tím giống hình
        /// </summary>
        private void CreateBackground(Canvas canvas)
        {
            GameObject bgObj = new GameObject("PixelBackground");
            bgObj.transform.SetParent(canvas.transform, false);

            RectTransform bgRect = bgObj.AddComponent<RectTransform>();
            bgRect.anchorMin = Vector2.zero;
            bgRect.anchorMax = Vector2.one;
            bgRect.offsetMin = Vector2.zero;
            bgRect.offsetMax = Vector2.zero;

            Image bgImage = bgObj.AddComponent<Image>();
            bgImage.color = backgroundColor;

            // Set as first child để làm background
            bgObj.transform.SetAsFirstSibling();
        }

        /// <summary>
        /// Tạo title "START GAME" giống hình
        /// </summary>
        private void CreateTitle(Canvas canvas)
        {
            GameObject titleObj = new GameObject("StartGameTitle");
            titleObj.transform.SetParent(canvas.transform, false);

            RectTransform titleRect = titleObj.AddComponent<RectTransform>();
            titleRect.anchorMin = new Vector2(0.5f, 0.7f);
            titleRect.anchorMax = new Vector2(0.5f, 0.7f);
            titleRect.anchoredPosition = Vector2.zero;
            titleRect.sizeDelta = new Vector2(800, 120);

            TextMeshProUGUI titleText = titleObj.AddComponent<TextMeshProUGUI>();
            titleText.text = "START GAME";
            titleText.fontSize = 72;
            titleText.color = titleColor;
            titleText.alignment = TextAlignmentOptions.Center;
            titleText.fontStyle = FontStyles.Bold;

            // Load Gixel font cho pixel style
            TMP_FontAsset gixelFont = Resources.Load<TMP_FontAsset>("Sprites/UI/Gixel SDF");
            if (gixelFont == null) gixelFont = Resources.Load<TMP_FontAsset>("UI/Gixel SDF");
            if (gixelFont == null) gixelFont = Resources.Load<TMP_FontAsset>("Gixel SDF");

            if (gixelFont != null)
                titleText.font = gixelFont;

            // Add outline đen giống hình
            titleText.outlineWidth = 0.3f;
            titleText.outlineColor = Color.black;
        }
