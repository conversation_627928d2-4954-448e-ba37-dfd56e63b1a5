using UnityEngine;
using UnityEngine.SceneManagement;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Menu
{
    public class MainMenu : MonoBehaviour
    {
        [Header("Game Menu Style")]
        [SerializeField] private bool applyGameStyleOnStart = true;

        private void Start()
        {
            if (applyGameStyleOnStart)
            {
                ApplyGameMenuStyle();
            }
        }

        /// <summary>
        /// Apply game menu style với assets có sẵn
        /// </summary>
        public void ApplyGameMenuStyle()
        {
            GameMenuStyler styler = FindObjectOfType<GameMenuStyler>();
            if (styler == null)
            {
                GameObject stylerObject = new GameObject("GameMenuStyler");
                styler = stylerObject.AddComponent<GameMenuStyler>();
            }

            styler.ApplyGameMenuStyle();
        }

        public void Play()
        {
            Debug.Log("Play button pressed!");
            SceneManager.LoadScene("Scene1");
        }

        public void Quit()
        {
            Debug.Log("Quit button pressed!");

            #if UNITY_EDITOR
                EditorApplication.isPlaying = false;
            #else
                Application.Quit();
            #endif
        }
    }
}
