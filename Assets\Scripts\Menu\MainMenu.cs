using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using TMPro;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Menu
{
    /// <summary>
    /// MainMenu tạo menu giống hình với chỉ 1 file duy nhất
    /// </summary>
    public class MainMenu : MonoBehaviour
    {
        [Header("Auto Create Beautiful Menu")]
        [SerializeField] private bool createMenuOnStart = true;

        // Colors giống hình
        private readonly Color panelColor = new Color(0.85f, 0.7f, 0.3f, 1f); // Vàng đồng
        private readonly Color titleBgColor = new Color(0.9f, 0.8f, 0.4f, 1f); // Vàng sáng cho title
        private readonly Color easyColor = new Color(1f, 0.9f, 0.3f, 1f); // Vàng Easy
        private readonly Color mediumColor = new Color(1f, 0.7f, 0.2f, 1f); // Cam Medium
        private readonly Color hardColor = new Color(1f, 0.4f, 0.1f, 1f); // Đỏ cam Hard
        private readonly Color textColor = new Color(0.3f, 0.2f, 0.1f, 1f); // Nâu đậm

        private void Start()
        {
            if (createMenuOnStart)
            {
                CreateBeautifulMenu();
            }
        }

        /// <summary>
        /// Tạo menu đẹp giống hình chỉ với assets có sẵn
        /// </summary>
        [ContextMenu("Create Beautiful Menu")]
        public void CreateBeautifulMenu()
        {
            Debug.Log("Creating beautiful menu like the image...");

            // Xóa UI cũ
            ClearOldUI();

            // Tạo menu mới giống hình
            CreateMainPanel();
            CreateTitleSection();
            CreateDifficultyButtons();
            CreateCloseButton();

            // Add background music
            AddBackgroundMusic();

            Debug.Log("Beautiful menu created!");
        }

        /// <summary>
        /// Xóa UI cũ
        /// </summary>
        private void ClearOldUI()
        {
            // Tìm và xóa các UI elements cũ
            Button[] oldButtons = FindObjectsOfType<Button>();
            TextMeshProUGUI[] oldTexts = FindObjectsOfType<TextMeshProUGUI>();

            foreach (Button btn in oldButtons)
            {
                if (btn.gameObject != gameObject)
                    DestroyImmediate(btn.gameObject);
            }

            foreach (TextMeshProUGUI txt in oldTexts)
            {
                if (txt.gameObject != gameObject)
                    DestroyImmediate(txt.gameObject);
            }
        }

        /// <summary>
        /// Tạo panel chính giống hình
        /// </summary>
        private void CreateMainPanel()
        {
            Canvas canvas = FindObjectOfType<Canvas>();
            if (canvas == null)
            {
                GameObject canvasObj = new GameObject("Canvas");
                canvas = canvasObj.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvasObj.AddComponent<CanvasScaler>();
                canvasObj.AddComponent<GraphicRaycaster>();
            }

            // Setup Canvas Scaler
            CanvasScaler scaler = canvas.GetComponent<CanvasScaler>();
            scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            scaler.referenceResolution = new Vector2(1920, 1080);
            scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
            scaler.matchWidthOrHeight = 0.5f;

            // Tạo main panel
            GameObject panel = new GameObject("DifficultyPanel");
            panel.transform.SetParent(canvas.transform, false);

            RectTransform panelRect = panel.AddComponent<RectTransform>();
            panelRect.anchorMin = new Vector2(0.5f, 0.5f);
            panelRect.anchorMax = new Vector2(0.5f, 0.5f);
            panelRect.anchoredPosition = Vector2.zero;
            panelRect.sizeDelta = new Vector2(500, 400); // Kích thước giống hình

            // Add Image với BoxWhiteOutline
            Image panelImage = panel.AddComponent<Image>();
            Sprite boxSprite = Resources.Load<Sprite>("Sprites/UI/BoxWhiteOutline");
            if (boxSprite == null) boxSprite = Resources.Load<Sprite>("UI/BoxWhiteOutline");

            if (boxSprite != null)
            {
                panelImage.sprite = boxSprite;
                panelImage.type = Image.Type.Sliced;
                panelImage.color = panelColor;
            }
            else
            {
                panelImage.color = panelColor;
            }
        }

        /// <summary>
        /// Tạo phần title "DIFFICULTY" giống hình
        /// </summary>
        private void CreateTitleSection()
        {
            GameObject panel = GameObject.Find("DifficultyPanel");
            if (panel == null) return;

            // Tạo title background
            GameObject titleBg = new GameObject("TitleBackground");
            titleBg.transform.SetParent(panel.transform, false);

            RectTransform titleBgRect = titleBg.AddComponent<RectTransform>();
            titleBgRect.anchorMin = new Vector2(0.1f, 0.75f);
            titleBgRect.anchorMax = new Vector2(0.9f, 0.95f);
            titleBgRect.offsetMin = Vector2.zero;
            titleBgRect.offsetMax = Vector2.zero;

            Image titleBgImage = titleBg.AddComponent<Image>();
            Sprite boxSprite = Resources.Load<Sprite>("Sprites/UI/Box");
            if (boxSprite == null) boxSprite = Resources.Load<Sprite>("UI/Box");

            if (boxSprite != null)
            {
                titleBgImage.sprite = boxSprite;
                titleBgImage.type = Image.Type.Sliced;
                titleBgImage.color = titleBgColor;
            }
            else
            {
                titleBgImage.color = titleBgColor;
            }

            // Tạo title text
            GameObject titleText = new GameObject("TitleText");
            titleText.transform.SetParent(titleBg.transform, false);

            RectTransform titleTextRect = titleText.AddComponent<RectTransform>();
            titleTextRect.anchorMin = Vector2.zero;
            titleTextRect.anchorMax = Vector2.one;
            titleTextRect.offsetMin = Vector2.zero;
            titleTextRect.offsetMax = Vector2.zero;

            TextMeshProUGUI titleTMP = titleText.AddComponent<TextMeshProUGUI>();
            titleTMP.text = "DIFFICULTY";
            titleTMP.fontSize = 32;
            titleTMP.color = textColor;
            titleTMP.alignment = TextAlignmentOptions.Center;
            titleTMP.fontStyle = FontStyles.Bold;

            // Load Gixel font
            TMP_FontAsset gixelFont = Resources.Load<TMP_FontAsset>("Sprites/UI/Gixel SDF");
            if (gixelFont == null) gixelFont = Resources.Load<TMP_FontAsset>("UI/Gixel SDF");
            if (gixelFont == null) gixelFont = Resources.Load<TMP_FontAsset>("Gixel SDF");

            if (gixelFont != null)
                titleTMP.font = gixelFont;

            // Add outline
            titleTMP.outlineWidth = 0.2f;
            titleTMP.outlineColor = Color.black;
        }

        /// <summary>
        /// Tạo 3 buttons EASY, MEDIUM, HARD giống hình
        /// </summary>
        private void CreateDifficultyButtons()
        {
            GameObject panel = GameObject.Find("DifficultyPanel");
            if (panel == null) return;

            string[] buttonTexts = { "EASY", "MEDIUM", "HARD" };
            Color[] buttonColors = { easyColor, mediumColor, hardColor };

            for (int i = 0; i < 3; i++)
            {
                CreateDifficultyButton(panel, buttonTexts[i], buttonColors[i], i);
            }
        }

        /// <summary>
        /// Tạo 1 difficulty button
        /// </summary>
        private void CreateDifficultyButton(GameObject parent, string text, Color color, int index)
        {
            GameObject buttonObj = new GameObject($"{text}Button");
            buttonObj.transform.SetParent(parent.transform, false);

            RectTransform buttonRect = buttonObj.AddComponent<RectTransform>();
            buttonRect.anchorMin = new Vector2(0.15f, 0.6f - (index * 0.15f));
            buttonRect.anchorMax = new Vector2(0.85f, 0.7f - (index * 0.15f));
            buttonRect.offsetMin = Vector2.zero;
            buttonRect.offsetMax = Vector2.zero;

            // Add Image
            Image buttonImage = buttonObj.AddComponent<Image>();
            Sprite boxSprite = Resources.Load<Sprite>("Sprites/UI/Box");
            if (boxSprite == null) boxSprite = Resources.Load<Sprite>("UI/Box");

            if (boxSprite != null)
            {
                buttonImage.sprite = boxSprite;
                buttonImage.type = Image.Type.Sliced;
            }

            // Add Button component
            Button button = buttonObj.AddComponent<Button>();

            // Setup colors giống hình với gradient effect
            ColorBlock colors = button.colors;
            colors.normalColor = color;
            colors.highlightedColor = Color.Lerp(color, Color.white, 0.2f);
            colors.pressedColor = Color.Lerp(color, Color.black, 0.3f);
            colors.selectedColor = colors.highlightedColor;
            colors.fadeDuration = 0.1f;
            button.colors = colors;

            // Add text
            GameObject textObj = new GameObject("Text");
            textObj.transform.SetParent(buttonObj.transform, false);

            RectTransform textRect = textObj.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;

            TextMeshProUGUI buttonText = textObj.AddComponent<TextMeshProUGUI>();
            buttonText.text = text;
            buttonText.fontSize = 24;
            buttonText.color = textColor;
            buttonText.alignment = TextAlignmentOptions.Center;
            buttonText.fontStyle = FontStyles.Bold;

            // Load Gixel font
            TMP_FontAsset gixelFont = Resources.Load<TMP_FontAsset>("Sprites/UI/Gixel SDF");
            if (gixelFont == null) gixelFont = Resources.Load<TMP_FontAsset>("UI/Gixel SDF");
            if (gixelFont == null) gixelFont = Resources.Load<TMP_FontAsset>("Gixel SDF");

            if (gixelFont != null)
                buttonText.font = gixelFont;

            // Add outline
            buttonText.outlineWidth = 0.15f;
            buttonText.outlineColor = new Color(0, 0, 0, 0.8f);

            // Add button functionality
            switch (text)
            {
                case "EASY":
                    button.onClick.AddListener(() => PlayEasy());
                    break;
                case "MEDIUM":
                    button.onClick.AddListener(() => PlayMedium());
                    break;
                case "HARD":
                    button.onClick.AddListener(() => PlayHard());
                    break;
            }
        }

        /// <summary>
        /// Tạo nút X đóng giống hình
        /// </summary>
        private void CreateCloseButton()
        {
            GameObject panel = GameObject.Find("DifficultyPanel");
            if (panel == null) return;

            GameObject closeButtonObj = new GameObject("CloseButton");
            closeButtonObj.transform.SetParent(panel.transform, false);

            RectTransform closeRect = closeButtonObj.AddComponent<RectTransform>();
            closeRect.anchorMin = new Vector2(0.5f, 0.05f);
            closeRect.anchorMax = new Vector2(0.5f, 0.05f);
            closeRect.anchoredPosition = Vector2.zero;
            closeRect.sizeDelta = new Vector2(60, 60);

            // Add Image
            Image closeImage = closeButtonObj.AddComponent<Image>();
            Sprite boxSprite = Resources.Load<Sprite>("Sprites/UI/Box");
            if (boxSprite == null) boxSprite = Resources.Load<Sprite>("UI/Box");

            if (boxSprite != null)
            {
                closeImage.sprite = boxSprite;
                closeImage.type = Image.Type.Sliced;
            }

            closeImage.color = new Color(0.9f, 0.8f, 0.4f, 1f); // Vàng giống title

            // Add Button
            Button closeButton = closeButtonObj.AddComponent<Button>();
            ColorBlock colors = closeButton.colors;
            colors.normalColor = Color.white;
            colors.highlightedColor = new Color(1f, 0.9f, 0.9f, 1f);
            colors.pressedColor = new Color(0.8f, 0.8f, 0.8f, 1f);
            closeButton.colors = colors;

            // Add X text
            GameObject xTextObj = new GameObject("XText");
            xTextObj.transform.SetParent(closeButtonObj.transform, false);

            RectTransform xTextRect = xTextObj.AddComponent<RectTransform>();
            xTextRect.anchorMin = Vector2.zero;
            xTextRect.anchorMax = Vector2.one;
            xTextRect.offsetMin = Vector2.zero;
            xTextRect.offsetMax = Vector2.zero;

            TextMeshProUGUI xText = xTextObj.AddComponent<TextMeshProUGUI>();
            xText.text = "✕";
            xText.fontSize = 32;
            xText.color = textColor;
            xText.alignment = TextAlignmentOptions.Center;
            xText.fontStyle = FontStyles.Bold;

            // Add outline
            xText.outlineWidth = 0.2f;
            xText.outlineColor = Color.black;

            // Add close functionality
            closeButton.onClick.AddListener(() => Quit());
        }

        /// <summary>
        /// Add background music
        /// </summary>
        private void AddBackgroundMusic()
        {
            AudioSource audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();

            AudioClip bgMusic = Resources.Load<AudioClip>("8Bit Music - 062022/1. Track 1");
            if (bgMusic == null)
                bgMusic = Resources.Load<AudioClip>("1. Track 1");

            if (bgMusic != null)
            {
                audioSource.clip = bgMusic;
                audioSource.loop = true;
                audioSource.volume = 0.3f;
                audioSource.playOnAwake = false;

                if (!audioSource.isPlaying)
                    audioSource.Play();
            }
        }

        // Button functions
        public void PlayEasy()
        {
            Debug.Log("EASY difficulty selected!");
            SceneManager.LoadScene("Scene1");
        }

        public void PlayMedium()
        {
            Debug.Log("MEDIUM difficulty selected!");
            SceneManager.LoadScene("Scene1");
        }

        public void PlayHard()
        {
            Debug.Log("HARD difficulty selected!");
            SceneManager.LoadScene("Scene1");
        }

        public void Play()
        {
            Debug.Log("Play button pressed!");
            SceneManager.LoadScene("Scene1");
        }

        public void Quit()
        {
            Debug.Log("Quit button pressed!");

            #if UNITY_EDITOR
                EditorApplication.isPlaying = false;
            #else
                Application.Quit();
            #endif
        }
    }
}
