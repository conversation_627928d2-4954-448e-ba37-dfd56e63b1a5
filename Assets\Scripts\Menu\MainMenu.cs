using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Menu
{
    public class MainMenu : MonoBehaviour
    {
        [Header("Menu Styling")]
        [SerializeField] private bool autoStyleOnStart = true;
        [SerializeField] private MenuStyler menuStyler;

        [Header("Scene Transition")]
        [SerializeField] private float transitionDelay = 0.5f;
        [SerializeField] private bool useFadeTransition = true;

        private void Start()
        {
            // Auto-apply menu styling if enabled
            if (autoStyleOnStart)
            {
                ApplyMenuStyling();
            }
        }

        /// <summary>
        /// Apply beautiful styling to the menu
        /// </summary>
        public void ApplyMenuStyling()
        {
            // Find or create MenuStyler
            if (menuStyler == null)
            {
                menuStyler = FindObjectOfType<MenuStyler>();
                if (menuStyler == null)
                {
                    GameObject stylerObject = new GameObject("MenuStyler");
                    menuStyler = stylerObject.AddComponent<MenuStyler>();
                }
            }

            // Apply styling
            StartCoroutine(menuStyler.ApplyMenuStyling());
        }

        /// <summary>
        /// Play button - start game
        /// </summary>
        public void Play()
        {
            Debug.Log("Play button pressed!");

            if (useFadeTransition)
            {
                StartCoroutine(PlayWithTransition());
            }
            else
            {
                SceneManager.LoadScene("Scene1");
            }
        }

        /// <summary>
        /// Play with smooth transition
        /// </summary>
        private IEnumerator PlayWithTransition()
        {
            // Add fade out effect here if UIFade is available
            var uiFade = FindObjectOfType<Managements.UIFade>();
            if (uiFade != null)
            {
                uiFade.FadeToBlack();
                yield return new WaitForSeconds(transitionDelay);
            }

            SceneManager.LoadScene("Scene1");
        }

        /// <summary>
        /// Quit button - exit game
        /// </summary>
        public void Quit()
        {
            Debug.Log("Quit button pressed!");

            #if UNITY_EDITOR
                EditorApplication.isPlaying = false;
            #else
                Application.Quit();
            #endif
        }

        /// <summary>
        /// Options button - show options menu
        /// </summary>
        public void ShowOptions()
        {
            Debug.Log("Options button pressed!");

            // Find and show options menu
            GameObject optionsMenu = GameObject.Find("OptionsMenu");
            if (optionsMenu != null)
            {
                optionsMenu.SetActive(true);
            }
        }

        /// <summary>
        /// Back button - hide options menu
        /// </summary>
        public void HideOptions()
        {
            Debug.Log("Back button pressed!");

            // Find and hide options menu
            GameObject optionsMenu = GameObject.Find("OptionsMenu");
            if (optionsMenu != null)
            {
                optionsMenu.SetActive(false);
            }
        }

        /// <summary>
        /// Reset menu styling
        /// </summary>
        [ContextMenu("Reset Menu Style")]
        public void ResetMenuStyle()
        {
            if (menuStyler != null)
            {
                menuStyler.ResetMenuStyle();
            }
        }
    }
}
