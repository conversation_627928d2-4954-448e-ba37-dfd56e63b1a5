using UnityEngine;
using UnityEngine.UI;

namespace Menu
{
    /// <summary>
    /// Script đơn giản để sửa kích thước UI của MainMenu
    /// Attach script này vào Canvas hoặc GameObject bất kỳ trong MainMenu scene
    /// </summary>
    public class MainMenuUIFixer : MonoBehaviour
    {
        [Header("Quick Fix Settings")]
        [SerializeField] private float uiScaleReduction = 0.6f; // Giảm UI xuống 60% kích thước hiện tại
        [SerializeField] private bool fixOnStart = true;
        [SerializeField] private bool fixCanvasScaler = true;
        [SerializeField] private bool fixOversizedElements = true;
        
        [Header("Thresholds")]
        [SerializeField] private float maxAllowedScale = 2.0f; // Elements có scale > 2.0 sẽ được điều chỉnh
        
        private void Start()
        {
            if (fixOnStart)
            {
                FixMainMenuUI();
            }
        }
        
        /// <summary>
        /// Sửa tất cả vấn đề về kích thước UI trong MainMenu
        /// </summary>
        [ContextMenu("Fix MainMenu UI Size")]
        public void FixMainMenuUI()
        {
            Debug.Log("MainMenuUIFixer: Starting UI size fix...");
            
            if (fixCanvasScaler)
            {
                FixCanvasScalers();
            }
            
            if (fixOversizedElements)
            {
                FixOversizedElements();
            }
            
            Debug.Log("MainMenuUIFixer: UI size fix completed!");
        }
        
        /// <summary>
        /// Sửa Canvas Scaler để UI nhỏ hơn
        /// </summary>
        private void FixCanvasScalers()
        {
            Canvas[] canvases = FindObjectsOfType<Canvas>();
            
            foreach (Canvas canvas in canvases)
            {
                CanvasScaler scaler = canvas.GetComponent<CanvasScaler>();
                if (scaler != null)
                {
                    Vector2 originalResolution = scaler.referenceResolution;
                    
                    // Tăng reference resolution để UI hiển thị nhỏ hơn
                    Vector2 newResolution = new Vector2(
                        originalResolution.x / uiScaleReduction,
                        originalResolution.y / uiScaleReduction
                    );
                    
                    scaler.referenceResolution = newResolution;
                    
                    Debug.Log($"MainMenuUIFixer: Fixed Canvas '{canvas.name}' resolution: {originalResolution} -> {newResolution}");
                }
            }
        }
        
        /// <summary>
        /// Sửa các elements có scale quá lớn
        /// </summary>
        private void FixOversizedElements()
        {
            Transform[] allTransforms = FindObjectsOfType<Transform>();
            int fixedCount = 0;
            
            foreach (Transform t in allTransforms)
            {
                // Chỉ xử lý UI elements (có RectTransform)
                if (t.GetComponent<RectTransform>() != null)
                {
                    if (IsOversized(t))
                    {
                        Vector3 originalScale = t.localScale;
                        Vector3 newScale = originalScale * uiScaleReduction;
                        
                        t.localScale = newScale;
                        fixedCount++;
                        
                        Debug.Log($"MainMenuUIFixer: Fixed '{t.name}' scale: {originalScale} -> {newScale}");
                    }
                }
            }
            
            Debug.Log($"MainMenuUIFixer: Fixed {fixedCount} oversized elements");
        }
        
        /// <summary>
        /// Kiểm tra xem element có quá lớn không
        /// </summary>
        private bool IsOversized(Transform t)
        {
            return t.localScale.x > maxAllowedScale || 
                   t.localScale.y > maxAllowedScale || 
                   t.localScale.z > maxAllowedScale;
        }
        
        /// <summary>
        /// Khôi phục kích thước gốc (để test)
        /// </summary>
        [ContextMenu("Restore Original Size")]
        public void RestoreOriginalSize()
        {
            // Reset Canvas Scalers về 1920x1080
            Canvas[] canvases = FindObjectsOfType<Canvas>();
            foreach (Canvas canvas in canvases)
            {
                CanvasScaler scaler = canvas.GetComponent<CanvasScaler>();
                if (scaler != null)
                {
                    scaler.referenceResolution = new Vector2(1920, 1080);
                }
            }
            
            Debug.Log("MainMenuUIFixer: Restored original Canvas resolution");
        }
        
        /// <summary>
        /// Áp dụng scale factor tùy chỉnh
        /// </summary>
        public void ApplyCustomScale(float scaleFactor)
        {
            uiScaleReduction = Mathf.Clamp(scaleFactor, 0.1f, 1.0f);
            FixMainMenuUI();
        }
    }
}
