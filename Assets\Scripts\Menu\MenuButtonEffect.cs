using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Collections;

namespace Menu
{
    /// <summary>
    /// Tạo hiệu ứng cho button menu
    /// </summary>
    public class MenuButtonEffect : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IPointerEnter<PERSON><PERSON><PERSON>, IPointerExit<PERSON><PERSON>ler, IPointerDownHandler, IPointerUpHandler
    {
        [Header("Scale Effect")]
        [SerializeField] private float hoverScale = 1.1f;
        [SerializeField] private float pressScale = 0.95f;
        [SerializeField] private float animationSpeed = 0.2f;
        
        [Header("Audio")]
        [SerializeField] private AudioClip hoverSound;
        [SerializeField] private AudioClip clickSound;
        [SerializeField] private float soundVolume = 0.5f;
        
        [Header("Glow Effect")]
        [SerializeField] private bool enableGlow = true;
        [SerializeField] private Color glowColor = Color.white;
        [SerializeField] private float glowIntensity = 0.3f;
        
        private Vector3 originalScale;
        private AudioSource audioSource;
        private Button button;
        private Image buttonImage;
        private Coroutine currentAnimation;
        private bool isHovering = false;
        
        private void Awake()
        {
            button = GetComponent<Button>();
            buttonImage = GetComponent<Image>();
            originalScale = transform.localScale;
            
            // Setup audio source
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
            
            audioSource.playOnAwake = false;
            audioSource.volume = soundVolume;
        }
        
        /// <summary>
        /// Initialize effect settings
        /// </summary>
        public void Initialize(float hoverScale, float animSpeed, AudioClip hoverSound, AudioClip clickSound)
        {
            this.hoverScale = hoverScale;
            this.animationSpeed = animSpeed;
            this.hoverSound = hoverSound;
            this.clickSound = clickSound;
        }
        
        /// <summary>
        /// Mouse enter - hover effect
        /// </summary>
        public void OnPointerEnter(PointerEventData eventData)
        {
            if (!button.interactable) return;
            
            isHovering = true;
            
            // Play hover sound
            PlaySound(hoverSound);
            
            // Scale animation
            AnimateScale(originalScale * hoverScale);
            
            // Glow effect
            if (enableGlow && buttonImage != null)
            {
                StartCoroutine(AnimateGlow(glowIntensity));
            }
        }
        
        /// <summary>
        /// Mouse exit - return to normal
        /// </summary>
        public void OnPointerExit(PointerEventData eventData)
        {
            if (!button.interactable) return;
            
            isHovering = false;
            
            // Return to original scale
            AnimateScale(originalScale);
            
            // Remove glow
            if (enableGlow && buttonImage != null)
            {
                StartCoroutine(AnimateGlow(0f));
            }
        }
        
        /// <summary>
        /// Mouse down - press effect
        /// </summary>
        public void OnPointerDown(PointerEventData eventData)
        {
            if (!button.interactable) return;
            
            // Play click sound
            PlaySound(clickSound);
            
            // Press scale
            AnimateScale(originalScale * pressScale);
        }
        
        /// <summary>
        /// Mouse up - return to hover or normal
        /// </summary>
        public void OnPointerUp(PointerEventData eventData)
        {
            if (!button.interactable) return;
            
            // Return to hover scale if still hovering, otherwise normal
            Vector3 targetScale = isHovering ? originalScale * hoverScale : originalScale;
            AnimateScale(targetScale);
        }
        
        /// <summary>
        /// Animate scale change
        /// </summary>
        private void AnimateScale(Vector3 targetScale)
        {
            if (currentAnimation != null)
            {
                StopCoroutine(currentAnimation);
            }
            
            currentAnimation = StartCoroutine(ScaleAnimation(targetScale));
        }
        
        /// <summary>
        /// Scale animation coroutine
        /// </summary>
        private IEnumerator ScaleAnimation(Vector3 targetScale)
        {
            Vector3 startScale = transform.localScale;
            float elapsed = 0f;
            
            while (elapsed < animationSpeed)
            {
                elapsed += Time.unscaledDeltaTime;
                float t = elapsed / animationSpeed;
                
                // Smooth animation curve
                t = Mathf.SmoothStep(0f, 1f, t);
                
                transform.localScale = Vector3.Lerp(startScale, targetScale, t);
                
                yield return null;
            }
            
            transform.localScale = targetScale;
            currentAnimation = null;
        }
        
        /// <summary>
        /// Animate glow effect
        /// </summary>
        private IEnumerator AnimateGlow(float targetIntensity)
        {
            if (buttonImage == null) yield break;
            
            Color startColor = buttonImage.color;
            Color targetColor = Color.Lerp(startColor, glowColor, targetIntensity);
            
            float elapsed = 0f;
            
            while (elapsed < animationSpeed)
            {
                elapsed += Time.unscaledDeltaTime;
                float t = elapsed / animationSpeed;
                
                buttonImage.color = Color.Lerp(startColor, targetColor, t);
                
                yield return null;
            }
            
            buttonImage.color = targetColor;
        }
        
        /// <summary>
        /// Play sound effect
        /// </summary>
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.clip = clip;
                audioSource.Play();
            }
        }
        
        /// <summary>
        /// Add floating animation
        /// </summary>
        public void StartFloatingAnimation()
        {
            StartCoroutine(FloatingAnimation());
        }
        
        /// <summary>
        /// Floating animation for special buttons
        /// </summary>
        private IEnumerator FloatingAnimation()
        {
            Vector3 basePosition = transform.localPosition;
            float floatAmount = 5f;
            float speed = 2f;
            
            while (enabled)
            {
                float newY = basePosition.y + Mathf.Sin(Time.time * speed) * floatAmount;
                transform.localPosition = new Vector3(basePosition.x, newY, basePosition.z);
                
                yield return null;
            }
        }
        
        /// <summary>
        /// Add pulse effect
        /// </summary>
        public void StartPulseEffect()
        {
            StartCoroutine(PulseEffect());
        }
        
        /// <summary>
        /// Pulse effect animation
        /// </summary>
        private IEnumerator PulseEffect()
        {
            float pulseSpeed = 1.5f;
            float pulseAmount = 0.1f;
            
            while (enabled)
            {
                float scale = 1f + Mathf.Sin(Time.time * pulseSpeed) * pulseAmount;
                transform.localScale = originalScale * scale;
                
                yield return null;
            }
        }
        
        /// <summary>
        /// Reset to original state
        /// </summary>
        public void ResetToOriginal()
        {
            if (currentAnimation != null)
            {
                StopCoroutine(currentAnimation);
                currentAnimation = null;
            }
            
            transform.localScale = originalScale;
            
            if (buttonImage != null)
            {
                // Reset color to original button color
                ColorBlock colors = button.colors;
                buttonImage.color = colors.normalColor;
            }
        }
        
        private void OnDisable()
        {
            ResetToOriginal();
        }
    }
}
