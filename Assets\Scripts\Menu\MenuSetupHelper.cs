using UnityEngine;
using UnityEngine.UI;
using TMPro;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Menu
{
    /// <summary>
    /// Helper script để setup menu đẹp một cách tự động
    /// </summary>
    public class MenuSetupHelper : MonoBehaviour
    {
        [Header("Quick Setup")]
        [SerializeField] private bool setupOnAwake = true;
        
        [Header("Assets References")]
        [SerializeField] private TMP_FontAsset pixelFont;
        [SerializeField] private Sprite buttonSprite;
        [SerializeField] private AudioClip backgroundMusic;
        
        [Header("Style Presets")]
        [SerializeField] private MenuStylePreset stylePreset = MenuStylePreset.PixelArt;
        
        public enum MenuStylePreset
        {
            PixelArt,
            Modern,
            Retro,
            Minimal
        }
        
        private void Awake()
        {
            if (setupOnAwake)
            {
                SetupMenu();
            }
        }
        
        /// <summary>
        /// Setup menu với preset được chọn
        /// </summary>
        [ContextMenu("Setup Menu")]
        public void SetupMenu()
        {
            Debug.Log("MenuSetupHelper: Setting up menu...");
            
            // Load assets if not assigned
            LoadRequiredAssets();
            
            // Apply preset
            ApplyStylePreset();
            
            // Setup MenuStyler
            SetupMenuStyler();
            
            Debug.Log("MenuSetupHelper: Menu setup completed!");
        }
        
        /// <summary>
        /// Load assets cần thiết
        /// </summary>
        private void LoadRequiredAssets()
        {
            // Load pixel font
            if (pixelFont == null)
            {
                pixelFont = Resources.Load<TMP_FontAsset>("Fonts & Materials/Gixel SDF");
                if (pixelFont == null)
                {
                    // Try alternative paths
                    pixelFont = Resources.Load<TMP_FontAsset>("Gixel SDF");
                }
                
                if (pixelFont == null)
                {
                    Debug.LogWarning("MenuSetupHelper: Could not find Gixel font. Using default font.");
                }
            }
            
            // Load button sprite
            if (buttonSprite == null)
            {
                buttonSprite = Resources.Load<Sprite>("Sprites/UI/BoxWhiteOutline");
                if (buttonSprite == null)
                {
                    buttonSprite = Resources.Load<Sprite>("UI/BoxWhiteOutline");
                }
            }
            
            // Load background music
            if (backgroundMusic == null)
            {
                backgroundMusic = Resources.Load<AudioClip>("8Bit Music - 062022/1. Track 1");
                if (backgroundMusic == null)
                {
                    Debug.LogWarning("MenuSetupHelper: Could not find background music.");
                }
            }
        }
        
        /// <summary>
        /// Apply style preset
        /// </summary>
        private void ApplyStylePreset()
        {
            MenuStyler styler = GetOrCreateMenuStyler();
            
            switch (stylePreset)
            {
                case MenuStylePreset.PixelArt:
                    ApplyPixelArtPreset(styler);
                    break;
                case MenuStylePreset.Modern:
                    ApplyModernPreset(styler);
                    break;
                case MenuStylePreset.Retro:
                    ApplyRetroPreset(styler);
                    break;
                case MenuStylePreset.Minimal:
                    ApplyMinimalPreset(styler);
                    break;
            }
        }
        
        /// <summary>
        /// Get hoặc tạo MenuStyler component
        /// </summary>
        private MenuStyler GetOrCreateMenuStyler()
        {
            MenuStyler styler = FindObjectOfType<MenuStyler>();
            if (styler == null)
            {
                GameObject stylerObject = new GameObject("MenuStyler");
                styler = stylerObject.AddComponent<MenuStyler>();
            }
            
            return styler;
        }
        
        /// <summary>
        /// Apply Pixel Art preset
        /// </summary>
        private void ApplyPixelArtPreset(MenuStyler styler)
        {
            // Colors for pixel art style
            Color darkBlue = new Color(0.1f, 0.15f, 0.3f, 1f);
            Color mediumBlue = new Color(0.2f, 0.3f, 0.5f, 1f);
            Color lightBlue = new Color(0.3f, 0.4f, 0.6f, 1f);
            Color yellow = new Color(1f, 0.9f, 0.3f, 1f);
            
            // Use reflection to set private fields (for demo purposes)
            SetStylerField(styler, "backgroundColor", darkBlue);
            SetStylerField(styler, "buttonNormalColor", mediumBlue);
            SetStylerField(styler, "buttonHoverColor", lightBlue);
            SetStylerField(styler, "titleColor", yellow);
            SetStylerField(styler, "usePixelFont", true);
            SetStylerField(styler, "addBackgroundMusic", true);
            SetStylerField(styler, "enableGlowEffect", true);
        }
        
        /// <summary>
        /// Apply Modern preset
        /// </summary>
        private void ApplyModernPreset(MenuStyler styler)
        {
            Color darkGray = new Color(0.15f, 0.15f, 0.15f, 1f);
            Color mediumGray = new Color(0.3f, 0.3f, 0.3f, 1f);
            Color lightGray = new Color(0.5f, 0.5f, 0.5f, 1f);
            Color accent = new Color(0.2f, 0.6f, 1f, 1f);
            
            SetStylerField(styler, "backgroundColor", darkGray);
            SetStylerField(styler, "buttonNormalColor", mediumGray);
            SetStylerField(styler, "buttonHoverColor", lightGray);
            SetStylerField(styler, "titleColor", accent);
            SetStylerField(styler, "usePixelFont", false);
            SetStylerField(styler, "enableGlowEffect", false);
        }
        
        /// <summary>
        /// Apply Retro preset
        /// </summary>
        private void ApplyRetroPreset(MenuStyler styler)
        {
            Color darkPurple = new Color(0.2f, 0.1f, 0.3f, 1f);
            Color mediumPurple = new Color(0.4f, 0.2f, 0.5f, 1f);
            Color lightPurple = new Color(0.6f, 0.3f, 0.7f, 1f);
            Color pink = new Color(1f, 0.4f, 0.8f, 1f);
            
            SetStylerField(styler, "backgroundColor", darkPurple);
            SetStylerField(styler, "buttonNormalColor", mediumPurple);
            SetStylerField(styler, "buttonHoverColor", lightPurple);
            SetStylerField(styler, "titleColor", pink);
            SetStylerField(styler, "usePixelFont", true);
            SetStylerField(styler, "addBackgroundMusic", true);
            SetStylerField(styler, "enableGlowEffect", true);
        }
        
        /// <summary>
        /// Apply Minimal preset
        /// </summary>
        private void ApplyMinimalPreset(MenuStyler styler)
        {
            Color white = Color.white;
            Color lightGray = new Color(0.9f, 0.9f, 0.9f, 1f);
            Color mediumGray = new Color(0.7f, 0.7f, 0.7f, 1f);
            Color black = Color.black;
            
            SetStylerField(styler, "backgroundColor", white);
            SetStylerField(styler, "buttonNormalColor", lightGray);
            SetStylerField(styler, "buttonHoverColor", mediumGray);
            SetStylerField(styler, "titleColor", black);
            SetStylerField(styler, "textColor", black);
            SetStylerField(styler, "usePixelFont", false);
            SetStylerField(styler, "addBackgroundMusic", false);
            SetStylerField(styler, "enableGlowEffect", false);
        }
        
        /// <summary>
        /// Setup MenuStyler component
        /// </summary>
        private void SetupMenuStyler()
        {
            MenuStyler styler = GetOrCreateMenuStyler();
            
            // Apply styling
            StartCoroutine(styler.ApplyMenuStyling());
        }
        
        /// <summary>
        /// Helper method để set private fields (using reflection)
        /// </summary>
        private void SetStylerField(MenuStyler styler, string fieldName, object value)
        {
            var field = typeof(MenuStyler).GetField(fieldName, 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (field != null)
            {
                field.SetValue(styler, value);
            }
            else
            {
                Debug.LogWarning($"MenuSetupHelper: Could not find field '{fieldName}' in MenuStyler");
            }
        }
        
        /// <summary>
        /// Quick setup buttons for different presets
        /// </summary>
        [ContextMenu("Apply Pixel Art Style")]
        public void ApplyPixelArtStyle()
        {
            stylePreset = MenuStylePreset.PixelArt;
            SetupMenu();
        }
        
        [ContextMenu("Apply Modern Style")]
        public void ApplyModernStyle()
        {
            stylePreset = MenuStylePreset.Modern;
            SetupMenu();
        }
        
        [ContextMenu("Apply Retro Style")]
        public void ApplyRetroStyle()
        {
            stylePreset = MenuStylePreset.Retro;
            SetupMenu();
        }
        
        [ContextMenu("Apply Minimal Style")]
        public void ApplyMinimalStyle()
        {
            stylePreset = MenuStylePreset.Minimal;
            SetupMenu();
        }
        
        #if UNITY_EDITOR
        /// <summary>
        /// Editor utility để tạo menu setup
        /// </summary>
        [MenuItem("Tools/Menu/Setup Beautiful Menu")]
        public static void CreateMenuSetup()
        {
            GameObject setupObject = new GameObject("MenuSetupHelper");
            setupObject.AddComponent<MenuSetupHelper>();
            
            Selection.activeGameObject = setupObject;
            
            Debug.Log("MenuSetupHelper created! Check the inspector for setup options.");
        }
        #endif
    }
}
