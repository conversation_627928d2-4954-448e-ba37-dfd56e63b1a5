using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;

namespace Menu
{
    /// <summary>
    /// Nâng cấp menu hiện có thành menu đẹp với assets có sẵn
    /// </summary>
    public class MenuStyler : MonoBehaviour
    {
        [Header("Style Settings")]
        [SerializeField] private bool applyOnStart = true;
        [SerializeField] private bool usePixelFont = true;
        [SerializeField] private bool addBackgroundMusic = true;
        [SerializeField] private bool addButtonEffects = true;
        [SerializeField] private bool addBackground = true;
        
        [Header("Colors")]
        [SerializeField] private Color backgroundColor = new Color(0.1f, 0.15f, 0.25f, 1f);
        [SerializeField] private Color buttonNormalColor = new Color(0.2f, 0.3f, 0.5f, 1f);
        [SerializeField] private Color buttonHoverColor = new Color(0.3f, 0.4f, 0.6f, 1f);
        [SerializeField] private Color buttonPressColor = new Color(0.1f, 0.2f, 0.4f, 1f);
        [SerializeField] private Color textColor = Color.white;
        [SerializeField] private Color titleColor = new Color(1f, 0.9f, 0.3f, 1f);
        
        [Header("Button Settings")]
        [SerializeField] private Vector2 buttonPadding = new Vector2(20f, 10f);
        [SerializeField] private float buttonSpacing = 15f;
        [SerializeField] private int buttonFontSize = 28;
        [SerializeField] private int titleFontSize = 48;
        
        [Header("Animation")]
        [SerializeField] private float hoverScale = 1.1f;
        [SerializeField] private float animationSpeed = 0.2f;
        [SerializeField] private bool enableGlowEffect = true;
        
        [Header("Audio")]
        [SerializeField] private AudioClip backgroundMusic;
        [SerializeField] private AudioClip hoverSound;
        [SerializeField] private AudioClip clickSound;
        
        private Canvas mainCanvas;
        private AudioSource audioSource;
        private TMP_FontAsset pixelFont;
        
        private void Start()
        {
            if (applyOnStart)
            {
                StartCoroutine(ApplyMenuStyling());
            }
        }
        
        /// <summary>
        /// Áp dụng styling cho menu
        /// </summary>
        [ContextMenu("Apply Menu Styling")]
        public IEnumerator ApplyMenuStyling()
        {
            Debug.Log("MenuStyler: Starting menu styling...");
            
            // 1. Setup cơ bản
            SetupCanvas();
            LoadAssets();
            
            yield return new WaitForEndOfFrame();
            
            // 2. Style background
            if (addBackground)
            {
                CreateStyledBackground();
            }
            
            // 3. Style buttons
            StyleAllButtons();
            
            // 4. Style text elements
            StyleAllTexts();
            
            // 5. Setup audio
            if (addBackgroundMusic)
            {
                SetupBackgroundMusic();
            }
            
            // 6. Add effects
            if (addButtonEffects)
            {
                AddButtonEffects();
            }
            
            Debug.Log("MenuStyler: Menu styling completed!");
        }
        
        /// <summary>
        /// Setup Canvas
        /// </summary>
        private void SetupCanvas()
        {
            mainCanvas = FindObjectOfType<Canvas>();
            if (mainCanvas == null)
            {
                Debug.LogError("MenuStyler: No Canvas found!");
                return;
            }
            
            // Ensure proper Canvas Scaler settings
            CanvasScaler scaler = mainCanvas.GetComponent<CanvasScaler>();
            if (scaler != null)
            {
                scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
                scaler.referenceResolution = new Vector2(1920, 1080);
                scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
                scaler.matchWidthOrHeight = 0.5f;
            }
        }
        
        /// <summary>
        /// Load assets cần thiết
        /// </summary>
        private void LoadAssets()
        {
            // Load pixel font
            if (usePixelFont)
            {
                pixelFont = Resources.Load<TMP_FontAsset>("Fonts & Materials/Gixel SDF");
                if (pixelFont == null)
                {
                    // Try alternative path
                    pixelFont = Resources.Load<TMP_FontAsset>("Gixel SDF");
                }
            }
            
            // Load background music nếu chưa assign
            if (backgroundMusic == null && addBackgroundMusic)
            {
                backgroundMusic = Resources.Load<AudioClip>("8Bit Music - 062022/1. Track 1");
            }
        }
        
        /// <summary>
        /// Tạo background đẹp
        /// </summary>
        private void CreateStyledBackground()
        {
            // Tìm hoặc tạo background
            GameObject bgObject = GameObject.Find("Background");
            if (bgObject == null)
            {
                bgObject = new GameObject("Background");
                bgObject.transform.SetParent(mainCanvas.transform, false);
                bgObject.transform.SetAsFirstSibling(); // Đặt ở dưới cùng
            }
            
            RectTransform bgRect = bgObject.GetComponent<RectTransform>();
            if (bgRect == null)
                bgRect = bgObject.AddComponent<RectTransform>();
            
            // Full screen
            bgRect.anchorMin = Vector2.zero;
            bgRect.anchorMax = Vector2.one;
            bgRect.offsetMin = Vector2.zero;
            bgRect.offsetMax = Vector2.zero;
            
            // Add gradient background
            Image bgImage = bgObject.GetComponent<Image>();
            if (bgImage == null)
                bgImage = bgObject.AddComponent<Image>();
            
            bgImage.color = backgroundColor;
            
            // Tạo gradient texture
            Texture2D gradientTexture = CreateGradientTexture();
            Sprite gradientSprite = Sprite.Create(gradientTexture, 
                new Rect(0, 0, gradientTexture.width, gradientTexture.height), 
                new Vector2(0.5f, 0.5f));
            
            bgImage.sprite = gradientSprite;
        }
        
        /// <summary>
        /// Tạo gradient texture cho background
        /// </summary>
        private Texture2D CreateGradientTexture()
        {
            int height = 256;
            Texture2D texture = new Texture2D(1, height);
            
            Color topColor = backgroundColor;
            Color bottomColor = new Color(backgroundColor.r * 0.5f, backgroundColor.g * 0.5f, backgroundColor.b * 0.5f, 1f);
            
            Color[] pixels = new Color[height];
            for (int i = 0; i < height; i++)
            {
                float t = (float)i / (height - 1);
                pixels[i] = Color.Lerp(bottomColor, topColor, t);
            }
            
            texture.SetPixels(pixels);
            texture.Apply();
            
            return texture;
        }
        
        /// <summary>
        /// Style tất cả buttons
        /// </summary>
        private void StyleAllButtons()
        {
            Button[] buttons = FindObjectsOfType<Button>();
            
            foreach (Button button in buttons)
            {
                StyleButton(button);
            }
        }
        
        /// <summary>
        /// Style một button
        /// </summary>
        private void StyleButton(Button button)
        {
            // Load UI sprite nếu có
            Sprite buttonSprite = LoadButtonSprite();
            
            Image buttonImage = button.GetComponent<Image>();
            if (buttonImage != null)
            {
                if (buttonSprite != null)
                {
                    buttonImage.sprite = buttonSprite;
                    buttonImage.type = Image.Type.Sliced;
                }
                
                // Set colors
                ColorBlock colors = button.colors;
                colors.normalColor = buttonNormalColor;
                colors.highlightedColor = buttonHoverColor;
                colors.pressedColor = buttonPressColor;
                colors.selectedColor = buttonHoverColor;
                colors.fadeDuration = animationSpeed;
                button.colors = colors;
            }
            
            // Style button text
            TextMeshProUGUI buttonText = button.GetComponentInChildren<TextMeshProUGUI>();
            if (buttonText != null)
            {
                if (pixelFont != null)
                    buttonText.font = pixelFont;
                
                buttonText.fontSize = buttonFontSize;
                buttonText.color = textColor;
                buttonText.fontStyle = FontStyles.Bold;
                
                // Add glow effect
                if (enableGlowEffect)
                {
                    buttonText.fontMaterial.SetFloat("_GlowPower", 0.2f);
                    buttonText.fontMaterial.SetColor("_GlowColor", Color.white);
                }
            }
            
            // Adjust button size
            RectTransform buttonRect = button.GetComponent<RectTransform>();
            if (buttonRect != null)
            {
                Vector2 currentSize = buttonRect.sizeDelta;
                buttonRect.sizeDelta = new Vector2(
                    Mathf.Max(currentSize.x, 200f) + buttonPadding.x,
                    Mathf.Max(currentSize.y, 50f) + buttonPadding.y
                );
            }
        }
        
        /// <summary>
        /// Load button sprite từ assets
        /// </summary>
        private Sprite LoadButtonSprite()
        {
            // Try to load UI sprites
            Sprite sprite = Resources.Load<Sprite>("Sprites/UI/BoxWhiteOutline");
            if (sprite == null)
                sprite = Resources.Load<Sprite>("UI/BoxWhiteOutline");
            if (sprite == null)
                sprite = Resources.Load<Sprite>("BoxWhiteOutline");
            
            return sprite;
        }
        
        /// <summary>
        /// Style tất cả text elements
        /// </summary>
        private void StyleAllTexts()
        {
            TextMeshProUGUI[] texts = FindObjectsOfType<TextMeshProUGUI>();
            
            foreach (TextMeshProUGUI text in texts)
            {
                StyleText(text);
            }
        }
        
        /// <summary>
        /// Style một text element
        /// </summary>
        private void StyleText(TextMeshProUGUI text)
        {
            if (pixelFont != null)
                text.font = pixelFont;
            
            // Determine if this is title text
            bool isTitle = text.name.ToLower().Contains("title") || 
                          text.text.ToLower().Contains("menu") ||
                          text.fontSize > 30;
            
            if (isTitle)
            {
                text.fontSize = titleFontSize;
                text.color = titleColor;
                text.fontStyle = FontStyles.Bold;
                
                // Add stronger glow for title
                if (enableGlowEffect)
                {
                    text.fontMaterial.SetFloat("_GlowPower", 0.4f);
                    text.fontMaterial.SetColor("_GlowColor", titleColor);
                }
            }
            else
            {
                text.color = textColor;
                
                if (enableGlowEffect)
                {
                    text.fontMaterial.SetFloat("_GlowPower", 0.1f);
                }
            }
        }
        
        /// <summary>
        /// Setup background music
        /// </summary>
        private void SetupBackgroundMusic()
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();
            
            if (backgroundMusic != null)
            {
                audioSource.clip = backgroundMusic;
                audioSource.loop = true;
                audioSource.volume = 0.3f;
                audioSource.playOnAwake = false;
                
                if (!audioSource.isPlaying)
                    audioSource.Play();
            }
        }
        
        /// <summary>
        /// Add button effects
        /// </summary>
        private void AddButtonEffects()
        {
            Button[] buttons = FindObjectsOfType<Button>();
            
            foreach (Button button in buttons)
            {
                // Add hover effect component
                MenuButtonEffect effect = button.gameObject.GetComponent<MenuButtonEffect>();
                if (effect == null)
                {
                    effect = button.gameObject.AddComponent<MenuButtonEffect>();
                }
                
                effect.Initialize(hoverScale, animationSpeed, hoverSound, clickSound);
            }
        }
        
        /// <summary>
        /// Reset menu về trạng thái gốc
        /// </summary>
        [ContextMenu("Reset Menu Style")]
        public void ResetMenuStyle()
        {
            // Stop background music
            if (audioSource != null && audioSource.isPlaying)
                audioSource.Stop();
            
            // Remove background
            GameObject bg = GameObject.Find("Background");
            if (bg != null)
                DestroyImmediate(bg);
            
            Debug.Log("MenuStyler: Menu style reset!");
        }
    }
}
