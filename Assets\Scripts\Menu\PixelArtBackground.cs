using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using System.Collections.Generic;

namespace Menu
{
    /// <summary>
    /// Tạo background animated theo phong cách pixel art
    /// </summary>
    public class PixelArtBackground : MonoBehaviour
    {
        [Header("Background Colors")]
        [SerializeField] private Color skyTopColor = new Color(0.1f, 0.2f, 0.5f, 1f);
        [SerializeField] private Color skyBottomColor = new Color(0.2f, 0.1f, 0.3f, 1f);
        [SerializeField] private Color mountainColor = new Color(0.1f, 0.1f, 0.2f, 1f);
        
        [Header("Stars")]
        [SerializeField] private int starCount = 100;
        [SerializeField] private float starTwinkleSpeed = 2f;
        [SerializeField] private Vector2 starSizeRange = new Vector2(1f, 3f);
        
        [Header("Clouds")]
        [SerializeField] private int cloudCount = 8;
        [SerializeField] private float cloudSpeed = 10f;
        [SerializeField] private Color cloudColor = new Color(0.4f, 0.7f, 1f, 0.8f);
        
        [Header("Mountains/Trees")]
        [SerializeField] private bool createMountains = true;
        [SerializeField] private int mountainLayers = 3;
        
        [Header("Animation")]
        [SerializeField] private bool animateBackground = true;
        [SerializeField] private float parallaxSpeed = 5f;
        
        private Canvas canvas;
        private List<GameObject> stars = new List<GameObject>();
        private List<GameObject> clouds = new List<GameObject>();
        private List<GameObject> mountainLayers = new List<GameObject>();
        
        private void Start()
        {
            canvas = GetComponent<Canvas>();
            if (canvas == null)
                canvas = GetComponentInParent<Canvas>();
            
            CreatePixelArtBackground();
        }
        
        /// <summary>
        /// Tạo toàn bộ background pixel art
        /// </summary>
        [ContextMenu("Create Pixel Art Background")]
        public void CreatePixelArtBackground()
        {
            CreateGradientSky();
            CreateStars();
            CreateClouds();
            
            if (createMountains)
            {
                CreateMountainLayers();
            }
            
            if (animateBackground)
            {
                StartBackgroundAnimation();
            }
            
            Debug.Log("PixelArtBackground: Created pixel art background!");
        }
        
        /// <summary>
        /// Tạo gradient sky background
        /// </summary>
        private void CreateGradientSky()
        {
            GameObject skyObject = new GameObject("Sky_Gradient");
            skyObject.transform.SetParent(transform, false);
            
            RectTransform skyRect = skyObject.AddComponent<RectTransform>();
            skyRect.anchorMin = Vector2.zero;
            skyRect.anchorMax = Vector2.one;
            skyRect.offsetMin = Vector2.zero;
            skyRect.offsetMax = Vector2.zero;
            
            // Tạo gradient texture
            Texture2D gradientTexture = CreateGradientTexture(256, skyTopColor, skyBottomColor);
            Sprite gradientSprite = Sprite.Create(gradientTexture, 
                new Rect(0, 0, gradientTexture.width, gradientTexture.height), 
                new Vector2(0.5f, 0.5f));
            
            Image skyImage = skyObject.AddComponent<Image>();
            skyImage.sprite = gradientSprite;
            skyImage.type = Image.Type.Stretched;
        }
        
        /// <summary>
        /// Tạo gradient texture
        /// </summary>
        private Texture2D CreateGradientTexture(int height, Color topColor, Color bottomColor)
        {
            Texture2D texture = new Texture2D(1, height);
            texture.filterMode = FilterMode.Bilinear;
            
            Color[] pixels = new Color[height];
            for (int i = 0; i < height; i++)
            {
                float t = (float)i / (height - 1);
                pixels[i] = Color.Lerp(bottomColor, topColor, t);
            }
            
            texture.SetPixels(pixels);
            texture.Apply();
            
            return texture;
        }
        
        /// <summary>
        /// Tạo stars với hiệu ứng twinkle
        /// </summary>
        private void CreateStars()
        {
            GameObject starsContainer = new GameObject("Stars_Container");
            starsContainer.transform.SetParent(transform, false);
            
            for (int i = 0; i < starCount; i++)
            {
                GameObject star = CreateStar(starsContainer.transform, i);
                stars.Add(star);
            }
        }
        
        /// <summary>
        /// Tạo một ngôi sao
        /// </summary>
        private GameObject CreateStar(Transform parent, int index)
        {
            GameObject star = new GameObject($"Star_{index}");
            star.transform.SetParent(parent, false);
            
            RectTransform starRect = star.AddComponent<RectTransform>();
            starRect.anchorMin = Vector2.zero;
            starRect.anchorMax = Vector2.zero;
            
            // Random size
            float size = Random.Range(starSizeRange.x, starSizeRange.y);
            starRect.sizeDelta = new Vector2(size, size);
            
            // Random position
            starRect.anchoredPosition = new Vector2(
                Random.Range(0, Screen.width),
                Random.Range(Screen.height * 0.3f, Screen.height) // Chỉ ở phần trên của màn hình
            );
            
            // Tạo star texture
            Texture2D starTexture = CreateStarTexture((int)size);
            Sprite starSprite = Sprite.Create(starTexture, 
                new Rect(0, 0, starTexture.width, starTexture.height), 
                new Vector2(0.5f, 0.5f));
            
            Image starImage = star.AddComponent<Image>();
            starImage.sprite = starSprite;
            starImage.color = Color.white;
            
            // Thêm twinkle animation
            StarTwinkle twinkle = star.AddComponent<StarTwinkle>();
            twinkle.Initialize(starTwinkleSpeed, Random.Range(0f, 2f));
            
            return star;
        }
        
        /// <summary>
        /// Tạo texture cho star
        /// </summary>
        private Texture2D CreateStarTexture(int size)
        {
            Texture2D texture = new Texture2D(size, size);
            texture.filterMode = FilterMode.Point;
            
            Color[] pixels = new Color[size * size];
            int center = size / 2;
            
            for (int y = 0; y < size; y++)
            {
                for (int x = 0; x < size; x++)
                {
                    int index = y * size + x;
                    
                    // Tạo hình ngôi sao đơn giản
                    if ((x == center && y >= center - 1 && y <= center + 1) ||
                        (y == center && x >= center - 1 && x <= center + 1))
                    {
                        pixels[index] = Color.white;
                    }
                    else
                    {
                        pixels[index] = Color.clear;
                    }
                }
            }
            
            texture.SetPixels(pixels);
            texture.Apply();
            
            return texture;
        }
        
        /// <summary>
        /// Tạo clouds
        /// </summary>
        private void CreateClouds()
        {
            GameObject cloudsContainer = new GameObject("Clouds_Container");
            cloudsContainer.transform.SetParent(transform, false);
            
            for (int i = 0; i < cloudCount; i++)
            {
                GameObject cloud = CreateCloud(cloudsContainer.transform, i);
                clouds.Add(cloud);
            }
        }
        
        /// <summary>
        /// Tạo một đám mây
        /// </summary>
        private GameObject CreateCloud(Transform parent, int index)
        {
            GameObject cloud = new GameObject($"Cloud_{index}");
            cloud.transform.SetParent(parent, false);
            
            RectTransform cloudRect = cloud.AddComponent<RectTransform>();
            cloudRect.anchorMin = Vector2.zero;
            cloudRect.anchorMax = Vector2.zero;
            
            // Random size và position
            Vector2 cloudSize = new Vector2(Random.Range(80, 150), Random.Range(40, 80));
            cloudRect.sizeDelta = cloudSize;
            cloudRect.anchoredPosition = new Vector2(
                Random.Range(-200, Screen.width + 200),
                Random.Range(Screen.height * 0.6f, Screen.height * 0.9f)
            );
            
            // Tạo cloud texture
            Texture2D cloudTexture = CreateCloudTexture((int)cloudSize.x, (int)cloudSize.y);
            Sprite cloudSprite = Sprite.Create(cloudTexture, 
                new Rect(0, 0, cloudTexture.width, cloudTexture.height), 
                new Vector2(0.5f, 0.5f));
            
            Image cloudImage = cloud.AddComponent<Image>();
            cloudImage.sprite = cloudSprite;
            cloudImage.color = cloudColor;
            
            // Thêm cloud movement
            CloudMover mover = cloud.AddComponent<CloudMover>();
            mover.Initialize(cloudSpeed, Random.Range(0.5f, 1.5f));
            
            return cloud;
        }
        
        /// <summary>
        /// Tạo texture cho cloud
        /// </summary>
        private Texture2D CreateCloudTexture(int width, int height)
        {
            Texture2D texture = new Texture2D(width, height);
            texture.filterMode = FilterMode.Point;
            
            Color[] pixels = new Color[width * height];
            
            // Tạo cloud shape đơn giản
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    int index = y * width + x;
                    
                    // Tạo hình oval đơn giản cho cloud
                    float centerX = width * 0.5f;
                    float centerY = height * 0.5f;
                    float radiusX = width * 0.4f;
                    float radiusY = height * 0.3f;
                    
                    float distance = Mathf.Pow((x - centerX) / radiusX, 2) + Mathf.Pow((y - centerY) / radiusY, 2);
                    
                    if (distance <= 1f)
                    {
                        pixels[index] = Color.white;
                    }
                    else
                    {
                        pixels[index] = Color.clear;
                    }
                }
            }
            
            texture.SetPixels(pixels);
            texture.Apply();
            
            return texture;
        }
        
        /// <summary>
        /// Tạo mountain layers
        /// </summary>
        private void CreateMountainLayers()
        {
            for (int i = 0; i < mountainLayers; i++)
            {
                GameObject layer = CreateMountainLayer(i);
                mountainLayers.Add(layer);
            }
        }
        
        /// <summary>
        /// Tạo một layer mountain
        /// </summary>
        private GameObject CreateMountainLayer(int layerIndex)
        {
            GameObject layer = new GameObject($"Mountain_Layer_{layerIndex}");
            layer.transform.SetParent(transform, false);
            
            RectTransform layerRect = layer.AddComponent<RectTransform>();
            layerRect.anchorMin = new Vector2(0, 0);
            layerRect.anchorMax = new Vector2(1, 0.4f - layerIndex * 0.1f);
            layerRect.offsetMin = Vector2.zero;
            layerRect.offsetMax = Vector2.zero;
            
            Image layerImage = layer.AddComponent<Image>();
            
            // Màu sắc khác nhau cho từng layer
            Color layerColor = Color.Lerp(mountainColor, Color.black, layerIndex * 0.3f);
            layerImage.color = layerColor;
            
            return layer;
        }
        
        /// <summary>
        /// Bắt đầu animation background
        /// </summary>
        private void StartBackgroundAnimation()
        {
            StartCoroutine(AnimateBackground());
        }
        
        /// <summary>
        /// Animate background elements
        /// </summary>
        private IEnumerator AnimateBackground()
        {
            while (animateBackground)
            {
                // Animate parallax cho mountain layers
                for (int i = 0; i < mountainLayers.Count; i++)
                {
                    if (mountainLayers[i] != null)
                    {
                        RectTransform rect = mountainLayers[i].GetComponent<RectTransform>();
                        Vector2 pos = rect.anchoredPosition;
                        pos.x -= parallaxSpeed * (i + 1) * Time.deltaTime;
                        
                        // Reset position khi ra khỏi màn hình
                        if (pos.x <= -Screen.width)
                            pos.x = Screen.width;
                        
                        rect.anchoredPosition = pos;
                    }
                }
                
                yield return null;
            }
        }
        
        /// <summary>
        /// Dọn dẹp khi destroy
        /// </summary>
        private void OnDestroy()
        {
            animateBackground = false;
        }
    }
}
