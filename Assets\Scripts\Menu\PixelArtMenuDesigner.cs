using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace Menu
{
    /// <summary>
    /// Tạo menu theo phong cách pixel art với thiết kế đẹp
    /// </summary>
    public class PixelArtMenuDesigner : MonoBehaviour
    {
        [Header("Menu Design Settings")]
        [SerializeField] private Color backgroundColor = new Color(0.1f, 0.2f, 0.4f, 1f); // Dark blue
        [SerializeField] private Color buttonColor = new Color(1f, 0.4f, 0.3f, 1f); // Orange-red
        [SerializeField] private Color buttonHoverColor = new Color(1f, 0.5f, 0.4f, 1f);
        [SerializeField] private Color textColor = Color.white;
        
        [Header("Button Settings")]
        [SerializeField] private Vector2 buttonSize = new Vector2(200f, 50f);
        [SerializeField] private float buttonSpacing = 20f;
        [SerializeField] private int buttonFontSize = 24;
        
        [Header("Animation Settings")]
        [SerializeField] private float hoverScaleMultiplier = 1.1f;
        [SerializeField] private float animationDuration = 0.2f;
        
        [Header("Background Elements")]
        [SerializeField] private bool createStars = true;
        [SerializeField] private int starCount = 50;
        [SerializeField] private bool createClouds = true;
        
        private Canvas mainCanvas;
        private GameObject backgroundPanel;
        
        private void Start()
        {
            CreatePixelArtMenu();
        }
        
        /// <summary>
        /// Tạo toàn bộ menu theo phong cách pixel art
        /// </summary>
        [ContextMenu("Create Pixel Art Menu")]
        public void CreatePixelArtMenu()
        {
            SetupCanvas();
            CreateBackground();
            CreateMenuButtons();
            CreateBackgroundElements();
            
            Debug.Log("PixelArtMenuDesigner: Created pixel art menu!");
        }
        
        /// <summary>
        /// Thiết lập Canvas chính
        /// </summary>
        private void SetupCanvas()
        {
            mainCanvas = GetComponent<Canvas>();
            if (mainCanvas == null)
            {
                mainCanvas = gameObject.AddComponent<Canvas>();
                gameObject.AddComponent<CanvasScaler>();
                gameObject.AddComponent<GraphicRaycaster>();
            }
            
            // Cấu hình Canvas Scaler
            CanvasScaler scaler = mainCanvas.GetComponent<CanvasScaler>();
            scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            scaler.referenceResolution = new Vector2(1920, 1080);
            scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
            scaler.matchWidthOrHeight = 0.5f;
        }
        
        /// <summary>
        /// Tạo background gradient
        /// </summary>
        private void CreateBackground()
        {
            GameObject bgObject = new GameObject("Background");
            bgObject.transform.SetParent(transform);
            
            RectTransform bgRect = bgObject.AddComponent<RectTransform>();
            bgRect.anchorMin = Vector2.zero;
            bgRect.anchorMax = Vector2.one;
            bgRect.offsetMin = Vector2.zero;
            bgRect.offsetMax = Vector2.zero;
            
            Image bgImage = bgObject.AddComponent<Image>();
            bgImage.color = backgroundColor;
            
            backgroundPanel = bgObject;
        }
        
        /// <summary>
        /// Tạo các button menu
        /// </summary>
        private void CreateMenuButtons()
        {
            string[] buttonTexts = { "PLAY", "OPTIONS", "QUIT" };
            string[] buttonMethods = { "Play", "Options", "Quit" };
            
            GameObject menuContainer = new GameObject("MenuContainer");
            menuContainer.transform.SetParent(transform);
            
            RectTransform containerRect = menuContainer.AddComponent<RectTransform>();
            containerRect.anchorMin = new Vector2(0.5f, 0.5f);
            containerRect.anchorMax = new Vector2(0.5f, 0.5f);
            containerRect.anchoredPosition = Vector2.zero;
            containerRect.sizeDelta = new Vector2(buttonSize.x, (buttonSize.y + buttonSpacing) * buttonTexts.Length);
            
            // Thêm Vertical Layout Group
            VerticalLayoutGroup layoutGroup = menuContainer.AddComponent<VerticalLayoutGroup>();
            layoutGroup.spacing = buttonSpacing;
            layoutGroup.childAlignment = TextAnchor.MiddleCenter;
            layoutGroup.childControlHeight = false;
            layoutGroup.childControlWidth = false;
            
            for (int i = 0; i < buttonTexts.Length; i++)
            {
                CreatePixelButton(menuContainer, buttonTexts[i], buttonMethods[i]);
            }
        }
        
        /// <summary>
        /// Tạo button theo phong cách pixel art
        /// </summary>
        private void CreatePixelButton(GameObject parent, string text, string methodName)
        {
            GameObject buttonObj = new GameObject($"Button_{text}");
            buttonObj.transform.SetParent(parent.transform);
            
            RectTransform buttonRect = buttonObj.AddComponent<RectTransform>();
            buttonRect.sizeDelta = buttonSize;
            
            // Tạo button component
            Button button = buttonObj.AddComponent<Button>();
            Image buttonImage = buttonObj.AddComponent<Image>();
            
            // Tạo pixel art style cho button
            CreatePixelButtonStyle(buttonImage);
            
            // Tạo text
            GameObject textObj = new GameObject("Text");
            textObj.transform.SetParent(buttonObj.transform);
            
            RectTransform textRect = textObj.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;
            
            TextMeshProUGUI buttonText = textObj.AddComponent<TextMeshProUGUI>();
            buttonText.text = text;
            buttonText.fontSize = buttonFontSize;
            buttonText.color = textColor;
            buttonText.alignment = TextAlignmentOptions.Center;
            buttonText.fontStyle = FontStyles.Bold;
            
            // Thêm hover effects
            AddButtonHoverEffect(button, buttonImage, buttonText);
            
            // Gán sự kiện click
            AssignButtonEvent(button, methodName);
        }
        
        /// <summary>
        /// Tạo style pixel art cho button
        /// </summary>
        private void CreatePixelButtonStyle(Image buttonImage)
        {
            // Tạo texture pixel art cho button
            Texture2D buttonTexture = CreatePixelButtonTexture();
            Sprite buttonSprite = Sprite.Create(buttonTexture, 
                new Rect(0, 0, buttonTexture.width, buttonTexture.height), 
                new Vector2(0.5f, 0.5f), 100f);
            
            buttonImage.sprite = buttonSprite;
            buttonImage.type = Image.Type.Sliced;
            buttonImage.color = buttonColor;
        }
        
        /// <summary>
        /// Tạo texture pixel art cho button
        /// </summary>
        private Texture2D CreatePixelButtonTexture()
        {
            int width = 32;
            int height = 16;
            Texture2D texture = new Texture2D(width, height);
            texture.filterMode = FilterMode.Point; // Pixel perfect
            
            Color[] pixels = new Color[width * height];
            
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    int index = y * width + x;
                    
                    // Tạo border
                    if (x == 0 || x == width - 1 || y == 0 || y == height - 1)
                    {
                        pixels[index] = Color.black;
                    }
                    // Tạo highlight ở trên
                    else if (y >= height - 3)
                    {
                        pixels[index] = Color.white * 0.3f;
                    }
                    // Tạo shadow ở dưới
                    else if (y <= 2)
                    {
                        pixels[index] = Color.black * 0.3f;
                    }
                    // Fill màu chính
                    else
                    {
                        pixels[index] = Color.white;
                    }
                }
            }
            
            texture.SetPixels(pixels);
            texture.Apply();
            
            return texture;
        }
        
        /// <summary>
        /// Thêm hiệu ứng hover cho button
        /// </summary>
        private void AddButtonHoverEffect(Button button, Image buttonImage, TextMeshProUGUI buttonText)
        {
            PixelButtonHover hoverEffect = button.gameObject.AddComponent<PixelButtonHover>();
            hoverEffect.Initialize(buttonImage, buttonText, hoverScaleMultiplier, animationDuration, buttonHoverColor);
        }
        
        /// <summary>
        /// Gán sự kiện cho button
        /// </summary>
        private void AssignButtonEvent(Button button, string methodName)
        {
            MainMenu mainMenu = FindObjectOfType<MainMenu>();
            if (mainMenu != null)
            {
                switch (methodName)
                {
                    case "Play":
                        button.onClick.AddListener(mainMenu.Play);
                        break;
                    case "Quit":
                        button.onClick.AddListener(mainMenu.Quit);
                        break;
                    case "Options":
                        // Thêm method Options vào MainMenu nếu cần
                        button.onClick.AddListener(() => Debug.Log("Options clicked"));
                        break;
                }
            }
        }
        
        /// <summary>
        /// Tạo các elements background (stars, clouds)
        /// </summary>
        private void CreateBackgroundElements()
        {
            if (createStars)
            {
                CreateStars();
            }
            
            if (createClouds)
            {
                CreateClouds();
            }
        }
        
        /// <summary>
        /// Tạo stars cho background
        /// </summary>
        private void CreateStars()
        {
            GameObject starsContainer = new GameObject("Stars");
            starsContainer.transform.SetParent(backgroundPanel.transform);
            
            for (int i = 0; i < starCount; i++)
            {
                GameObject star = new GameObject($"Star_{i}");
                star.transform.SetParent(starsContainer.transform);
                
                RectTransform starRect = star.AddComponent<RectTransform>();
                starRect.anchorMin = Vector2.zero;
                starRect.anchorMax = Vector2.zero;
                starRect.sizeDelta = new Vector2(4, 4);
                
                // Random position
                starRect.anchoredPosition = new Vector2(
                    Random.Range(0, Screen.width),
                    Random.Range(0, Screen.height)
                );
                
                Image starImage = star.AddComponent<Image>();
                starImage.color = Color.white;
                
                // Random opacity
                Color starColor = starImage.color;
                starColor.a = Random.Range(0.3f, 1f);
                starImage.color = starColor;
            }
        }
        
        /// <summary>
        /// Tạo clouds cho background
        /// </summary>
        private void CreateClouds()
        {
            // Placeholder cho clouds - có thể implement sau
            Debug.Log("PixelArtMenuDesigner: Cloud creation placeholder");
        }
    }
}
