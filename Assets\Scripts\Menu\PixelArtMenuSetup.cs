using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace Menu
{
    /// <summary>
    /// Script tổng hợp để setup toàn bộ Pixel Art Menu
    /// Chỉ cần attach script n<PERSON>y v<PERSON><PERSON> và click "Setup Complete Menu"
    /// </summary>
    public class PixelArtMenuSetup : Mono<PERSON>ehaviour
    {
        [Header("Menu Configuration")]
        [SerializeField] private bool setupOnStart = true;
        [SerializeField] private bool replaceExistingMenu = true;
        
        [Header("Design Settings")]
        [SerializeField] private Color backgroundColor = new Color(0.08f, 0.15f, 0.35f, 1f);
        [SerializeField] private Color buttonColor = new Color(0.9f, 0.4f, 0.3f, 1f);
        [SerializeField] private Color buttonHoverColor = new Color(1f, 0.5f, 0.4f, 1f);
        [SerializeField] private Color textColor = Color.white;
        
        [Header("Button Settings")]
        [SerializeField] private Vector2 buttonSize = new Vector2(250f, 60f);
        [SerializeField] private float buttonSpacing = 25f;
        [SerializeField] private int fontSize = 28;
        [SerializeField] private string fontAssetPath = ""; // Để trống sẽ dùng font mặc định
        
        [Header("Background Elements")]
        [SerializeField] private bool includeAnimatedBackground = true;
        [SerializeField] private bool includeStars = true;
        [SerializeField] private bool includeClouds = true;
        [SerializeField] private bool includeMountains = true;
        
        [Header("Audio")]
        [SerializeField] private AudioClip buttonHoverSound;
        [SerializeField] private AudioClip buttonClickSound;
        [SerializeField] private AudioClip backgroundMusic;
        
        private Canvas canvas;
        private CanvasScaler canvasScaler;
        
        private void Start()
        {
            if (setupOnStart)
            {
                SetupCompleteMenu();
            }
        }
        
        /// <summary>
        /// Setup toàn bộ menu pixel art
        /// </summary>
        [ContextMenu("Setup Complete Menu")]
        public void SetupCompleteMenu()
        {
            Debug.Log("PixelArtMenuSetup: Starting complete menu setup...");
            
            // 1. Setup Canvas
            SetupCanvas();
            
            // 2. Clear existing menu nếu cần
            if (replaceExistingMenu)
            {
                ClearExistingMenu();
            }
            
            // 3. Setup Background
            if (includeAnimatedBackground)
            {
                SetupAnimatedBackground();
            }
            else
            {
                SetupSimpleBackground();
            }
            
            // 4. Setup Menu Buttons
            SetupMenuButtons();
            
            // 5. Setup Audio
            SetupAudio();
            
            // 6. Apply UI Scaling
            ApplyUIScaling();
            
            Debug.Log("PixelArtMenuSetup: Complete menu setup finished!");
        }
        
        /// <summary>
        /// Setup Canvas và CanvasScaler
        /// </summary>
        private void SetupCanvas()
        {
            canvas = GetComponent<Canvas>();
            if (canvas == null)
            {
                canvas = gameObject.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                gameObject.AddComponent<GraphicRaycaster>();
            }
            
            canvasScaler = GetComponent<CanvasScaler>();
            if (canvasScaler == null)
            {
                canvasScaler = gameObject.AddComponent<CanvasScaler>();
            }
            
            // Cấu hình Canvas Scaler cho pixel perfect
            canvasScaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            canvasScaler.referenceResolution = new Vector2(1920, 1080);
            canvasScaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
            canvasScaler.matchWidthOrHeight = 0.5f;
            canvasScaler.referencePixelsPerUnit = 100f;
        }
        
        /// <summary>
        /// Xóa menu cũ nếu có
        /// </summary>
        private void ClearExistingMenu()
        {
            // Xóa các child objects cũ (trừ script components)
            for (int i = transform.childCount - 1; i >= 0; i--)
            {
                Transform child = transform.GetChild(i);
                if (child.gameObject != gameObject)
                {
                    DestroyImmediate(child.gameObject);
                }
            }
        }
        
        /// <summary>
        /// Setup animated background
        /// </summary>
        private void SetupAnimatedBackground()
        {
            GameObject backgroundObj = new GameObject("PixelArt_Background");
            backgroundObj.transform.SetParent(transform, false);
            
            PixelArtBackground backgroundScript = backgroundObj.AddComponent<PixelArtBackground>();
            
            // Cấu hình background settings
            // (Các settings sẽ được set thông qua Inspector của PixelArtBackground)
        }
        
        /// <summary>
        /// Setup simple background
        /// </summary>
        private void SetupSimpleBackground()
        {
            GameObject bgObj = new GameObject("Simple_Background");
            bgObj.transform.SetParent(transform, false);
            
            RectTransform bgRect = bgObj.AddComponent<RectTransform>();
            bgRect.anchorMin = Vector2.zero;
            bgRect.anchorMax = Vector2.one;
            bgRect.offsetMin = Vector2.zero;
            bgRect.offsetMax = Vector2.zero;
            
            Image bgImage = bgObj.AddComponent<Image>();
            bgImage.color = backgroundColor;
        }
        
        /// <summary>
        /// Setup menu buttons
        /// </summary>
        private void SetupMenuButtons()
        {
            GameObject menuObj = new GameObject("PixelArt_Menu");
            menuObj.transform.SetParent(transform, false);
            
            PixelArtMenuDesigner menuDesigner = menuObj.AddComponent<PixelArtMenuDesigner>();
            
            // Có thể cấu hình thêm settings cho menu designer ở đây
        }
        
        /// <summary>
        /// Setup audio system
        /// </summary>
        private void SetupAudio()
        {
            // Setup background music
            if (backgroundMusic != null)
            {
                GameObject audioObj = new GameObject("Background_Music");
                audioObj.transform.SetParent(transform, false);
                
                AudioSource audioSource = audioObj.AddComponent<AudioSource>();
                audioSource.clip = backgroundMusic;
                audioSource.loop = true;
                audioSource.playOnAwake = true;
                audioSource.volume = 0.3f;
                audioSource.Play();
            }
            
            // Setup UI sounds sẽ được handle bởi individual buttons
        }
        
        /// <summary>
        /// Apply UI scaling để đảm bảo menu hiển thị đúng kích thước
        /// </summary>
        private void ApplyUIScaling()
        {
            // Thêm UIScaleManager nếu chưa có
            UIScaleManager scaleManager = GetComponent<UIScaleManager>();
            if (scaleManager == null)
            {
                scaleManager = gameObject.AddComponent<UIScaleManager>();
            }
            
            // Hoặc sử dụng MainMenuUIFixer cho quick fix
            MainMenuUIFixer uiFixer = GetComponent<MainMenuUIFixer>();
            if (uiFixer == null)
            {
                uiFixer = gameObject.AddComponent<MainMenuUIFixer>();
            }
        }
        
        /// <summary>
        /// Tạo custom button với pixel art style
        /// </summary>
        public GameObject CreatePixelArtButton(string buttonText, Vector2 position, System.Action onClickAction)
        {
            GameObject buttonObj = new GameObject($"PixelButton_{buttonText}");
            buttonObj.transform.SetParent(transform, false);
            
            RectTransform buttonRect = buttonObj.AddComponent<RectTransform>();
            buttonRect.sizeDelta = buttonSize;
            buttonRect.anchoredPosition = position;
            
            Button button = buttonObj.AddComponent<Button>();
            Image buttonImage = buttonObj.AddComponent<Image>();
            buttonImage.color = buttonColor;
            
            // Add text
            GameObject textObj = new GameObject("Text");
            textObj.transform.SetParent(buttonObj.transform, false);
            
            RectTransform textRect = textObj.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;
            
            TextMeshProUGUI text = textObj.AddComponent<TextMeshProUGUI>();
            text.text = buttonText;
            text.fontSize = fontSize;
            text.color = textColor;
            text.alignment = TextAlignmentOptions.Center;
            text.fontStyle = FontStyles.Bold;
            
            // Add hover effect
            PixelButtonHover hoverEffect = buttonObj.AddComponent<PixelButtonHover>();
            hoverEffect.Initialize(buttonImage, text, 1.1f, 0.2f, buttonHoverColor);
            
            // Add click action
            if (onClickAction != null)
            {
                button.onClick.AddListener(() => onClickAction.Invoke());
            }
            
            return buttonObj;
        }
        
        /// <summary>
        /// Load font asset nếu có
        /// </summary>
        private TMP_FontAsset LoadFontAsset()
        {
            if (!string.IsNullOrEmpty(fontAssetPath))
            {
                return Resources.Load<TMP_FontAsset>(fontAssetPath);
            }
            return null;
        }
        
        /// <summary>
        /// Preview menu trong Editor
        /// </summary>
        [ContextMenu("Preview Menu")]
        public void PreviewMenu()
        {
            #if UNITY_EDITOR
            SetupCompleteMenu();
            Debug.Log("PixelArtMenuSetup: Menu preview created!");
            #endif
        }
        
        /// <summary>
        /// Reset menu về trạng thái ban đầu
        /// </summary>
        [ContextMenu("Reset Menu")]
        public void ResetMenu()
        {
            ClearExistingMenu();
            Debug.Log("PixelArtMenuSetup: Menu reset completed!");
        }
    }
}
