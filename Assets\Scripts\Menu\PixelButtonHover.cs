using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using TMPro;
using System.Collections;

namespace Menu
{
    /// <summary>
    /// Hiệu ứng hover cho button theo phong cách pixel art
    /// </summary>
    public class PixelButtonHover : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerEnterHandler, IPointerExitHandler, IPointerDownHandler, IPointerUpHandler
    {
        [Header("Hover Settings")]
        [SerializeField] private float hoverScale = 1.1f;
        [SerializeField] private float animationDuration = 0.2f;
        [SerializeField] private Color hoverColor = Color.white;
        
        [Header("Click Settings")]
        [SerializeField] private float clickScale = 0.95f;
        [SerializeField] private float clickDuration = 0.1f;
        
        [Header("Audio")]
        [SerializeField] private AudioClip hoverSound;
        [SerializeField] private AudioClip clickSound;
        
        private Image buttonImage;
        private TextMeshProUGUI buttonText;
        private Color originalColor;
        private Vector3 originalScale;
        private bool isHovering = false;
        private bool isClicking = false;
        
        private AudioSource audioSource;
        private Coroutine currentAnimation;
        
        /// <summary>
        /// Khởi tạo component với các tham số
        /// </summary>
        public void Initialize(Image image, TextMeshProUGUI text, float scale, float duration, Color hover)
        {
            buttonImage = image;
            buttonText = text;
            hoverScale = scale;
            animationDuration = duration;
            hoverColor = hover;
            
            Setup();
        }
        
        private void Awake()
        {
            Setup();
        }
        
        private void Setup()
        {
            if (buttonImage == null)
                buttonImage = GetComponent<Image>();
            
            if (buttonText == null)
                buttonText = GetComponentInChildren<TextMeshProUGUI>();
            
            if (buttonImage != null)
                originalColor = buttonImage.color;
            
            originalScale = transform.localScale;
            
            // Tạo AudioSource cho sound effects
            audioSource = gameObject.GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
                audioSource.volume = 0.5f;
            }
        }
        
        public void OnPointerEnter(PointerEventData eventData)
        {
            if (!isClicking)
            {
                isHovering = true;
                PlayHoverEffect();
                PlaySound(hoverSound);
            }
        }
        
        public void OnPointerExit(PointerEventData eventData)
        {
            if (!isClicking)
            {
                isHovering = false;
                PlayNormalEffect();
            }
        }
        
        public void OnPointerDown(PointerEventData eventData)
        {
            isClicking = true;
            PlayClickEffect();
            PlaySound(clickSound);
        }
        
        public void OnPointerUp(PointerEventData eventData)
        {
            isClicking = false;
            
            if (isHovering)
            {
                PlayHoverEffect();
            }
            else
            {
                PlayNormalEffect();
            }
        }
        
        /// <summary>
        /// Phát hiệu ứng hover
        /// </summary>
        private void PlayHoverEffect()
        {
            if (currentAnimation != null)
                StopCoroutine(currentAnimation);
            
            currentAnimation = StartCoroutine(AnimateButton(hoverScale, hoverColor, animationDuration));
        }
        
        /// <summary>
        /// Phát hiệu ứng normal
        /// </summary>
        private void PlayNormalEffect()
        {
            if (currentAnimation != null)
                StopCoroutine(currentAnimation);
            
            currentAnimation = StartCoroutine(AnimateButton(1f, originalColor, animationDuration));
        }
        
        /// <summary>
        /// Phát hiệu ứng click
        /// </summary>
        private void PlayClickEffect()
        {
            if (currentAnimation != null)
                StopCoroutine(currentAnimation);
            
            currentAnimation = StartCoroutine(AnimateButton(clickScale, hoverColor, clickDuration));
        }
        
        /// <summary>
        /// Animate button với scale và color
        /// </summary>
        private IEnumerator AnimateButton(float targetScale, Color targetColor, float duration)
        {
            Vector3 startScale = transform.localScale;
            Color startColor = buttonImage != null ? buttonImage.color : Color.white;
            
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                elapsed += Time.unscaledDeltaTime;
                float progress = elapsed / duration;
                
                // Sử dụng easing function cho animation mượt hơn
                float easedProgress = EaseOutBack(progress);
                
                // Animate scale
                Vector3 currentScale = Vector3.Lerp(startScale, originalScale * targetScale, easedProgress);
                transform.localScale = currentScale;
                
                // Animate color
                if (buttonImage != null)
                {
                    Color currentColor = Color.Lerp(startColor, targetColor, easedProgress);
                    buttonImage.color = currentColor;
                }
                
                // Animate text glow effect
                if (buttonText != null && targetScale > 1f)
                {
                    buttonText.fontMaterial.SetFloat("_GlowPower", Mathf.Lerp(0f, 0.3f, easedProgress));
                }
                else if (buttonText != null)
                {
                    buttonText.fontMaterial.SetFloat("_GlowPower", Mathf.Lerp(0.3f, 0f, easedProgress));
                }
                
                yield return null;
            }
            
            // Đảm bảo kết thúc ở giá trị chính xác
            transform.localScale = originalScale * targetScale;
            if (buttonImage != null)
                buttonImage.color = targetColor;
        }
        
        /// <summary>
        /// Easing function cho animation mượt mà
        /// </summary>
        private float EaseOutBack(float t)
        {
            const float c1 = 1.70158f;
            const float c3 = c1 + 1f;
            
            return 1f + c3 * Mathf.Pow(t - 1f, 3f) + c1 * Mathf.Pow(t - 1f, 2f);
        }
        
        /// <summary>
        /// Phát sound effect
        /// </summary>
        private void PlaySound(AudioClip clip)
        {
            if (clip != null && audioSource != null)
            {
                audioSource.clip = clip;
                audioSource.Play();
            }
        }
        
        /// <summary>
        /// Tạo hiệu ứng particle khi hover (optional)
        /// </summary>
        private void CreateHoverParticles()
        {
            // Có thể thêm particle effects sau
            GameObject particles = new GameObject("HoverParticles");
            particles.transform.SetParent(transform);
            
            // Thêm ParticleSystem component và cấu hình
            ParticleSystem ps = particles.AddComponent<ParticleSystem>();
            var main = ps.main;
            main.startLifetime = 0.5f;
            main.startSpeed = 2f;
            main.startSize = 0.1f;
            main.startColor = Color.white;
            main.maxParticles = 10;
            
            var emission = ps.emission;
            emission.rateOverTime = 20f;
            
            var shape = ps.shape;
            shape.shapeType = ParticleSystemShapeType.Rectangle;
            shape.scale = new Vector3(2f, 1f, 1f);
            
            // Tự động destroy sau 1 giây
            Destroy(particles, 1f);
        }
        
        /// <summary>
        /// Reset button về trạng thái ban đầu
        /// </summary>
        public void ResetButton()
        {
            if (currentAnimation != null)
                StopCoroutine(currentAnimation);
            
            transform.localScale = originalScale;
            if (buttonImage != null)
                buttonImage.color = originalColor;
            
            isHovering = false;
            isClicking = false;
        }
        
        private void OnDisable()
        {
            ResetButton();
        }
    }
}
