using UnityEngine;
using UnityEngine.UI;
using System.Collections;

namespace Menu
{
    /// <summary>
    /// Component để tạo hiệu ứng twinkle cho stars
    /// </summary>
    public class StarTwinkle : MonoBehaviour
    {
        [Header("Twinkle Settings")]
        [SerializeField] private float twinkleSpeed = 2f;
        [SerializeField] private float minAlpha = 0.3f;
        [SerializeField] private float maxAlpha = 1f;
        [SerializeField] private float delayOffset = 0f;
        
        private Image starImage;
        private Color originalColor;
        private Coroutine twinkleCoroutine;
        
        /// <summary>
        /// Khởi tạo star twinkle với tham số
        /// </summary>
        public void Initialize(float speed, float delay)
        {
            twinkleSpeed = speed;
            delayOffset = delay;
            Setup();
        }
        
        private void Start()
        {
            Setup();
        }
        
        private void Setup()
        {
            starImage = GetComponent<Image>();
            if (starImage != null)
            {
                originalColor = starImage.color;
                StartTwinkling();
            }
        }
        
        /// <summary>
        /// Bắt đầu hiệu ứng twinkle
        /// </summary>
        public void StartTwinkling()
        {
            if (twinkleCoroutine != null)
                StopCoroutine(twinkleCoroutine);
            
            twinkleCoroutine = StartCoroutine(TwinkleAnimation());
        }
        
        /// <summary>
        /// Dừng hiệu ứng twinkle
        /// </summary>
        public void StopTwinkling()
        {
            if (twinkleCoroutine != null)
            {
                StopCoroutine(twinkleCoroutine);
                twinkleCoroutine = null;
            }
            
            // Reset về màu gốc
            if (starImage != null)
                starImage.color = originalColor;
        }
        
        /// <summary>
        /// Animation twinkle
        /// </summary>
        private IEnumerator TwinkleAnimation()
        {
            // Delay ban đầu để tạo sự ngẫu nhiên
            yield return new WaitForSeconds(delayOffset);
            
            while (true)
            {
                // Fade out
                yield return StartCoroutine(FadeAlpha(maxAlpha, minAlpha, twinkleSpeed));
                
                // Fade in
                yield return StartCoroutine(FadeAlpha(minAlpha, maxAlpha, twinkleSpeed));
                
                // Random pause giữa các lần twinkle
                yield return new WaitForSeconds(Random.Range(0.5f, 2f));
            }
        }
        
        /// <summary>
        /// Fade alpha từ start đến end
        /// </summary>
        private IEnumerator FadeAlpha(float startAlpha, float endAlpha, float duration)
        {
            if (starImage == null) yield break;
            
            float elapsed = 0f;
            Color startColor = starImage.color;
            startColor.a = startAlpha;
            
            Color endColor = originalColor;
            endColor.a = endAlpha;
            
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float progress = elapsed / duration;
                
                // Sử dụng smooth curve
                float smoothProgress = Mathf.SmoothStep(0f, 1f, progress);
                
                Color currentColor = Color.Lerp(startColor, endColor, smoothProgress);
                starImage.color = currentColor;
                
                yield return null;
            }
            
            // Đảm bảo kết thúc ở giá trị chính xác
            starImage.color = endColor;
        }
        
        /// <summary>
        /// Tạo hiệu ứng twinkle đặc biệt (ví dụ khi hover)
        /// </summary>
        public void SpecialTwinkle()
        {
            StartCoroutine(SpecialTwinkleAnimation());
        }
        
        /// <summary>
        /// Animation twinkle đặc biệt
        /// </summary>
        private IEnumerator SpecialTwinkleAnimation()
        {
            if (starImage == null) yield break;
            
            Color originalColor = starImage.color;
            
            // Flash bright
            starImage.color = Color.white;
            yield return new WaitForSeconds(0.1f);
            
            // Fade back to normal
            float elapsed = 0f;
            float duration = 0.5f;
            
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float progress = elapsed / duration;
                
                Color currentColor = Color.Lerp(Color.white, originalColor, progress);
                starImage.color = currentColor;
                
                yield return null;
            }
            
            starImage.color = originalColor;
        }
        
        /// <summary>
        /// Thay đổi tốc độ twinkle
        /// </summary>
        public void SetTwinkleSpeed(float newSpeed)
        {
            twinkleSpeed = newSpeed;
        }
        
        /// <summary>
        /// Thay đổi range alpha
        /// </summary>
        public void SetAlphaRange(float min, float max)
        {
            minAlpha = Mathf.Clamp01(min);
            maxAlpha = Mathf.Clamp01(max);
        }
        
        private void OnEnable()
        {
            if (starImage != null)
                StartTwinkling();
        }
        
        private void OnDisable()
        {
            StopTwinkling();
        }
        
        private void OnDestroy()
        {
            StopTwinkling();
        }
    }
}
