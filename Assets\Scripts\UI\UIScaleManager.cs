using UnityEngine;
using UnityEngine.UI;

namespace UI
{
    /// <summary>
    /// Quản lý việc điều chỉnh kích thước UI để phù hợp với các màn hình khác nhau
    /// </summary>
    public class UIScaleManager : MonoBehaviour
    {
        [Header("UI Scale Settings")]
        [SerializeField] private float globalUIScale = 0.6f;
        [SerializeField] private bool scaleOnAwake = true;
        [SerializeField] private bool adaptToScreenSize = true;
        
        [Header("Reference Settings")]
        [SerializeField] private Vector2 targetResolution = new Vector2(1920, 1080);
        [SerializeField] private float maxScaleThreshold = 2.0f; // Elements với scale > 2.0 sẽ được điều chỉnh
        
        [Header("Debug")]
        [SerializeField] private bool enableDebugLogs = true;
        
        private void Awake()
        {
            if (scaleOnAwake)
            {
                ApplyUIScaling();
            }
        }
        
        /// <summary>
        /// Áp dụng scaling cho tất cả UI elements
        /// </summary>
        public void ApplyUIScaling()
        {
            if (adaptToScreenSize)
            {
                CalculateAdaptiveScale();
            }
            
            ScaleCanvases();
            ScaleUIElements();
            
            if (enableDebugLogs)
            {
                Debug.Log($"UIScaleManager: Applied UI scaling with factor {globalUIScale}");
            }
        }
        
        /// <summary>
        /// Tính toán scale factor dựa trên kích thước màn hình hiện tại
        /// </summary>
        private void CalculateAdaptiveScale()
        {
            float screenWidth = Screen.width;
            float screenHeight = Screen.height;
            
            // Tính tỷ lệ so với target resolution
            float widthRatio = screenWidth / targetResolution.x;
            float heightRatio = screenHeight / targetResolution.y;
            
            // Sử dụng tỷ lệ nhỏ hơn để đảm bảo UI không bị cắt
            float screenRatio = Mathf.Min(widthRatio, heightRatio);
            
            // Điều chỉnh global scale dựa trên screen ratio
            globalUIScale = Mathf.Clamp(globalUIScale * screenRatio, 0.3f, 1.0f);
            
            if (enableDebugLogs)
            {
                Debug.Log($"UIScaleManager: Screen {screenWidth}x{screenHeight}, calculated scale: {globalUIScale}");
            }
        }
        
        /// <summary>
        /// Điều chỉnh Canvas Scaler components
        /// </summary>
        private void ScaleCanvases()
        {
            Canvas[] canvases = FindObjectsOfType<Canvas>();
            
            foreach (Canvas canvas in canvases)
            {
                CanvasScaler scaler = canvas.GetComponent<CanvasScaler>();
                if (scaler != null && scaler.uiScaleMode == CanvasScaler.ScaleMode.ScaleWithScreenSize)
                {
                    // Điều chỉnh reference resolution
                    Vector2 originalResolution = scaler.referenceResolution;
                    Vector2 newResolution = new Vector2(
                        originalResolution.x / globalUIScale,
                        originalResolution.y / globalUIScale
                    );
                    
                    scaler.referenceResolution = newResolution;
                    
                    if (enableDebugLogs)
                    {
                        Debug.Log($"UIScaleManager: Canvas '{canvas.name}' resolution: {originalResolution} -> {newResolution}");
                    }
                }
            }
        }
        
        /// <summary>
        /// Điều chỉnh scale của các UI elements có scale quá lớn
        /// </summary>
        private void ScaleUIElements()
        {
            // Tìm tất cả RectTransform trong scene
            RectTransform[] rectTransforms = FindObjectsOfType<RectTransform>();
            
            foreach (RectTransform rectTransform in rectTransforms)
            {
                if (ShouldScaleElement(rectTransform))
                {
                    Vector3 originalScale = rectTransform.localScale;
                    Vector3 newScale = originalScale * globalUIScale;
                    
                    rectTransform.localScale = newScale;
                    
                    if (enableDebugLogs)
                    {
                        Debug.Log($"UIScaleManager: Scaled '{rectTransform.name}': {originalScale} -> {newScale}");
                    }
                }
            }
        }
        
        /// <summary>
        /// Kiểm tra xem element có cần được scale không
        /// </summary>
        private bool ShouldScaleElement(RectTransform rectTransform)
        {
            // Không scale Canvas root
            if (rectTransform.GetComponent<Canvas>() != null)
                return false;
                
            // Scale nếu có ít nhất một trục > maxScaleThreshold
            return rectTransform.localScale.x > maxScaleThreshold || 
                   rectTransform.localScale.y > maxScaleThreshold ||
                   rectTransform.localScale.z > maxScaleThreshold;
        }
        
        /// <summary>
        /// Reset tất cả UI về scale mặc định
        /// </summary>
        [ContextMenu("Reset UI Scale")]
        public void ResetUIScale()
        {
            RectTransform[] rectTransforms = FindObjectsOfType<RectTransform>();
            
            foreach (RectTransform rectTransform in rectTransforms)
            {
                if (rectTransform.localScale != Vector3.one)
                {
                    rectTransform.localScale = Vector3.one;
                }
            }
            
            // Reset Canvas Scalers
            CanvasScaler[] scalers = FindObjectsOfType<CanvasScaler>();
            foreach (CanvasScaler scaler in scalers)
            {
                scaler.referenceResolution = targetResolution;
            }
            
            if (enableDebugLogs)
            {
                Debug.Log("UIScaleManager: Reset all UI elements to default scale");
            }
        }
        
        /// <summary>
        /// Áp dụng scale factor tùy chỉnh
        /// </summary>
        public void ApplyCustomScale(float customScale)
        {
            globalUIScale = Mathf.Clamp(customScale, 0.1f, 2.0f);
            ApplyUIScaling();
        }
        
        /// <summary>
        /// Lấy scale factor hiện tại
        /// </summary>
        public float GetCurrentScale()
        {
            return globalUIScale;
        }
    }
}
