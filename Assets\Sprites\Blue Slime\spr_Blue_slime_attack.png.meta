fileFormatVersion: 2
guid: 714354ae4e0d850489a0b7345dc7b64c
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -3529484664382159344
    second: spr_<PERSON>_slime_attack_0
  - first:
      213: -5048223591721065087
    second: spr_Blue_slime_attack_1
  - first:
      213: -1043635249693406699
    second: spr_Blue_slime_attack_2
  - first:
      213: -4555020414000634115
    second: spr_Blue_slime_attack_3
  - first:
      213: -983264889815780976
    second: spr_Blue_slime_attack_4
  - first:
      213: -5958352419123694897
    second: spr_Blue_slime_attack_5
  - first:
      213: 6688923432856907823
    second: spr_Blue_slime_attack_6
  - first:
      213: -4382022280550477322
    second: spr_<PERSON>_slime_attack_7
  - first:
      213: -4806514478313635424
    second: spr_Blue_slime_attack_8
  - first:
      213: -5062141689237285490
    second: spr_Blue_slime_attack_9
  - first:
      213: -4263944490190795816
    second: spr_Blue_slime_attack_10
  - first:
      213: 6579427868927421713
    second: spr_Blue_slime_attack_11
  - first:
      213: 5069096781749399945
    second: spr_Blue_slime_attack_12
  - first:
      213: -2525814312224773379
    second: spr_Blue_slime_attack_13
  - first:
      213: 7578051565581373153
    second: spr_Blue_slime_attack_14
  - first:
      213: -1146612378448261916
    second: spr_Blue_slime_attack_15
  - first:
      213: 8587380214516945959
    second: spr_Blue_slime_attack_16
  - first:
      213: 6674575846193274551
    second: spr_Blue_slime_attack_17
  - first:
      213: -5093946391023785690
    second: spr_Blue_slime_attack_18
  - first:
      213: 8637935402190893721
    second: spr_Blue_slime_attack_19
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: spr_Blue_slime_attack_0
      rect:
        serializedVersion: 2
        x: 23
        y: 22
        width: 19
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0160053d560c40fc0800000000000000
      internalID: -3529484664382159344
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_1
      rect:
        serializedVersion: 2
        x: 87
        y: 22
        width: 19
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 18528b4d36b11f9b0800000000000000
      internalID: -5048223591721065087
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_2
      rect:
        serializedVersion: 2
        x: 151
        y: 22
        width: 19
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 51ac2f215434481f0800000000000000
      internalID: -1043635249693406699
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_3
      rect:
        serializedVersion: 2
        x: 215
        y: 22
        width: 19
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: df6e6d6211159c0c0800000000000000
      internalID: -4555020414000634115
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_4
      rect:
        serializedVersion: 2
        x: 279
        y: 22
        width: 19
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 091ba62dacdba52f0800000000000000
      internalID: -983264889815780976
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_5
      rect:
        serializedVersion: 2
        x: 343
        y: 22
        width: 19
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fc2fa832b0eaf4da0800000000000000
      internalID: -5958352419123694897
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_6
      rect:
        serializedVersion: 2
        x: 407
        y: 22
        width: 19
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f20f8393c64d3dc50800000000000000
      internalID: 6688923432856907823
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_7
      rect:
        serializedVersion: 2
        x: 471
        y: 22
        width: 19
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6f111f9e3fdef23c0800000000000000
      internalID: -4382022280550477322
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_8
      rect:
        serializedVersion: 2
        x: 472
        y: 36
        width: 16
        height: 19
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0a108584c84db4db0800000000000000
      internalID: -4806514478313635424
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_9
      rect:
        serializedVersion: 2
        x: 535
        y: 22
        width: 19
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e85d05a34f8afb9b0800000000000000
      internalID: -5062141689237285490
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_10
      rect:
        serializedVersion: 2
        x: 536
        y: 36
        width: 16
        height: 20
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8db8138d31d63d4c0800000000000000
      internalID: -4263944490190795816
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_11
      rect:
        serializedVersion: 2
        x: 599
        y: 22
        width: 19
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 11d69b899c2de4b50800000000000000
      internalID: 6579427868927421713
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_12
      rect:
        serializedVersion: 2
        x: 600
        y: 36
        width: 16
        height: 20
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 981bf307aac095640800000000000000
      internalID: 5069096781749399945
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_13
      rect:
        serializedVersion: 2
        x: 663
        y: 22
        width: 19
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dfaae3fe34182fcd0800000000000000
      internalID: -2525814312224773379
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_14
      rect:
        serializedVersion: 2
        x: 664
        y: 36
        width: 16
        height: 19
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1ee0218afb5aa2960800000000000000
      internalID: 7578051565581373153
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_15
      rect:
        serializedVersion: 2
        x: 727
        y: 22
        width: 19
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4ec8687ed1a6610f0800000000000000
      internalID: -1146612378448261916
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_16
      rect:
        serializedVersion: 2
        x: 728
        y: 36
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7241085aec08c2770800000000000000
      internalID: 8587380214516945959
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_17
      rect:
        serializedVersion: 2
        x: 791
        y: 22
        width: 19
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7be90c26e5bd0ac50800000000000000
      internalID: 6674575846193274551
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_18
      rect:
        serializedVersion: 2
        x: 855
        y: 22
        width: 19
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 629e42a0fbaae49b0800000000000000
      internalID: -5093946391023785690
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_Blue_slime_attack_19
      rect:
        serializedVersion: 2
        x: 919
        y: 22
        width: 19
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 992fb588b7c10e770800000000000000
      internalID: 8637935402190893721
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      spr_Blue_slime_attack_0: -3529484664382159344
      spr_Blue_slime_attack_1: -5048223591721065087
      spr_Blue_slime_attack_10: -4263944490190795816
      spr_Blue_slime_attack_11: 6579427868927421713
      spr_Blue_slime_attack_12: 5069096781749399945
      spr_Blue_slime_attack_13: -2525814312224773379
      spr_Blue_slime_attack_14: 7578051565581373153
      spr_Blue_slime_attack_15: -1146612378448261916
      spr_Blue_slime_attack_16: 8587380214516945959
      spr_Blue_slime_attack_17: 6674575846193274551
      spr_Blue_slime_attack_18: -5093946391023785690
      spr_Blue_slime_attack_19: 8637935402190893721
      spr_Blue_slime_attack_2: -1043635249693406699
      spr_Blue_slime_attack_3: -4555020414000634115
      spr_Blue_slime_attack_4: -983264889815780976
      spr_Blue_slime_attack_5: -5958352419123694897
      spr_Blue_slime_attack_6: 6688923432856907823
      spr_Blue_slime_attack_7: -4382022280550477322
      spr_Blue_slime_attack_8: -4806514478313635424
      spr_Blue_slime_attack_9: -5062141689237285490
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
