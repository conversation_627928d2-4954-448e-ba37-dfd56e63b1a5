fileFormatVersion: 2
guid: 0de177bcf0ef2c644bda9b463e0f3c7e
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 1925417606565882268
    second: spr_player_right_attack_0
  - first:
      213: 1228504236542681798
    second: spr_player_right_attack_1
  - first:
      213: 3861819835043493671
    second: spr_player_right_attack_2
  - first:
      213: 239768519284618219
    second: spr_player_right_attack_3
  - first:
      213: -291539360863411257
    second: spr_player_right_attack_4
  - first:
      213: -4393288114827582582
    second: spr_player_right_attack_5
  - first:
      213: -1610358376348479289
    second: spr_player_right_attack_6
  - first:
      213: 4108624642752802163
    second: spr_player_right_attack_7
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 16
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: spr_player_right_attack_0
      rect:
        serializedVersion: 2
        x: 23
        y: 17
        width: 17
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c9964a3ac1578ba10800000000000000
      internalID: 1925417606565882268
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_player_right_attack_1
      rect:
        serializedVersion: 2
        x: 87
        y: 17
        width: 17
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6c2603028168c0110800000000000000
      internalID: 1228504236542681798
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_player_right_attack_2
      rect:
        serializedVersion: 2
        x: 151
        y: 17
        width: 28
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 727335edda0f79530800000000000000
      internalID: 3861819835043493671
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_player_right_attack_3
      rect:
        serializedVersion: 2
        x: 215
        y: 17
        width: 39
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: be3f74ca234d35300800000000000000
      internalID: 239768519284618219
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_player_right_attack_4
      rect:
        serializedVersion: 2
        x: 279
        y: 17
        width: 32
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7c321b5fe7e34fbf0800000000000000
      internalID: -291539360863411257
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_player_right_attack_5
      rect:
        serializedVersion: 2
        x: 310
        y: 21
        width: 8
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a83a1c77cb7e703c0800000000000000
      internalID: -4393288114827582582
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_player_right_attack_6
      rect:
        serializedVersion: 2
        x: 341
        y: 17
        width: 40
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7c09ca8b69bd6a9e0800000000000000
      internalID: -1610358376348479289
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_player_right_attack_7
      rect:
        serializedVersion: 2
        x: 400
        y: 17
        width: 24
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 37d62ba1854c40930800000000000000
      internalID: 4108624642752802163
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      spr_player_right_attack_0: 1925417606565882268
      spr_player_right_attack_1: 1228504236542681798
      spr_player_right_attack_2: 3861819835043493671
      spr_player_right_attack_3: 239768519284618219
      spr_player_right_attack_4: -291539360863411257
      spr_player_right_attack_5: -4393288114827582582
      spr_player_right_attack_6: -1610358376348479289
      spr_player_right_attack_7: 4108624642752802163
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
