fileFormatVersion: 2
guid: 7e0b64b2638de6b43b97ace1bdd12b2e
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 8943998719802223876
    second: spr_player_right_idle_0
  - first:
      213: 7948810717990169345
    second: spr_player_right_idle_1
  - first:
      213: -577786430024881325
    second: spr_player_right_idle_2
  - first:
      213: -6353677351226980072
    second: spr_player_right_idle_3
  - first:
      213: 1547979238007046788
    second: spr_player_right_idle_4
  - first:
      213: -2454221239874624135
    second: spr_player_right_idle_5
  - first:
      213: -7869921119186624042
    second: spr_player_right_idle_6
  - first:
      213: 7909408967219603570
    second: spr_player_right_idle_7
  - first:
      213: 6516967968447163904
    second: spr_player_right_idle_8
  - first:
      213: -591873424371667864
    second: spr_player_right_idle_9
  - first:
      213: 6886733269701454518
    second: spr_player_right_idle_10
  - first:
      213: 2335730963168208952
    second: spr_player_right_idle_11
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 16
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: spr_player_right_idle_0
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.4}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 40d2c55c2777f1c70800000000000000
      internalID: 8943998719802223876
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_player_right_idle_1
      rect:
        serializedVersion: 2
        x: 64
        y: 0
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.4}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 10b2f90cb39df4e60800000000000000
      internalID: 7948810717990169345
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_player_right_idle_2
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.4}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 35b635a9d4a4bf7f0800000000000000
      internalID: -577786430024881325
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_player_right_idle_3
      rect:
        serializedVersion: 2
        x: 192
        y: 0
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.4}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 819cbaebc1433d7a0800000000000000
      internalID: -6353677351226980072
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_player_right_idle_4
      rect:
        serializedVersion: 2
        x: 256
        y: 0
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.4}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 48207195ae68b7510800000000000000
      internalID: 1547979238007046788
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_player_right_idle_5
      rect:
        serializedVersion: 2
        x: 320
        y: 0
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.4}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 975ed0d09cad0fdd0800000000000000
      internalID: -2454221239874624135
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_player_right_idle_6
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.4}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6d16980507c68c290800000000000000
      internalID: -7869921119186624042
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_player_right_idle_7
      rect:
        serializedVersion: 2
        x: 448
        y: 0
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.4}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 278e962cc8dd3cd60800000000000000
      internalID: 7909408967219603570
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_player_right_idle_8
      rect:
        serializedVersion: 2
        x: 512
        y: 0
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.4}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 00e64e3c6dbe07a50800000000000000
      internalID: 6516967968447163904
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_player_right_idle_9
      rect:
        serializedVersion: 2
        x: 576
        y: 0
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.4}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 86c2554a14e39c7f0800000000000000
      internalID: -591873424371667864
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_player_right_idle_10
      rect:
        serializedVersion: 2
        x: 640
        y: 0
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.4}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6ba6b6bdb67929f50800000000000000
      internalID: 6886733269701454518
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: spr_player_right_idle_11
      rect:
        serializedVersion: 2
        x: 704
        y: 0
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.4}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 838a58e8fee2a6020800000000000000
      internalID: 2335730963168208952
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 85d27b0cdcdef9d4f84627fbc786b4ac
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      spr_player_right_idle_0: 8943998719802223876
      spr_player_right_idle_1: 7948810717990169345
      spr_player_right_idle_10: 6886733269701454518
      spr_player_right_idle_11: 2335730963168208952
      spr_player_right_idle_2: -577786430024881325
      spr_player_right_idle_3: -6353677351226980072
      spr_player_right_idle_4: 1547979238007046788
      spr_player_right_idle_5: -2454221239874624135
      spr_player_right_idle_6: -7869921119186624042
      spr_player_right_idle_7: 7909408967219603570
      spr_player_right_idle_8: 6516967968447163904
      spr_player_right_idle_9: -591873424371667864
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
