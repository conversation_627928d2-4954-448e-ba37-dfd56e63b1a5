fileFormatVersion: 2
guid: a139aeb367e71204e80a543cc9435515
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -7229220149124856631
    second: Slash Effect_0
  - first:
      213: 8806561744824432989
    second: Slash Effect_1
  - first:
      213: -6246675371957094910
    second: Slash Effect_2
  - first:
      213: 5989712527177951153
    second: Slash Effect_3
  - first:
      213: 5379926381550770200
    second: Slash Effect_4
  - first:
      213: -4186273098142486107
    second: Slash Effect_5
  - first:
      213: -6598097557103341705
    second: Slash Effect_6
  - first:
      213: 4475434940280343158
    second: Slash Effect_7
  - first:
      213: 3335157376098573144
    second: Slash Effect_8
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 16
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Slash Effect_0
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9c401509396acab90800000000000000
      internalID: -7229220149124856631
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Slash Effect_1
      rect:
        serializedVersion: 2
        x: 64
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d59fdcc6f31373a70800000000000000
      internalID: 8806561744824432989
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Slash Effect_2
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 206f05378d95f49a0800000000000000
      internalID: -6246675371957094910
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Slash Effect_3
      rect:
        serializedVersion: 2
        x: 192
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1b726210fcbbf1350800000000000000
      internalID: 5989712527177951153
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Slash Effect_4
      rect:
        serializedVersion: 2
        x: 256
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 818161a3a8659aa40800000000000000
      internalID: 5379926381550770200
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Slash Effect_5
      rect:
        serializedVersion: 2
        x: 320
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5a1717c0cce57e5c0800000000000000
      internalID: -4186273098142486107
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Slash Effect_6
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 77f8b73ea39de64a0800000000000000
      internalID: -6598097557103341705
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Slash Effect_7
      rect:
        serializedVersion: 2
        x: 448
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 67a08c6cd50fb1e30800000000000000
      internalID: 4475434940280343158
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Slash Effect_8
      rect:
        serializedVersion: 2
        x: 512
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 85b116096fbd84e20800000000000000
      internalID: 3335157376098573144
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 6a6ddfefdb0779e4dbc2241725c26d56
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Slash Effect_0: -7229220149124856631
      Slash Effect_1: 8806561744824432989
      Slash Effect_2: -6246675371957094910
      Slash Effect_3: 5989712527177951153
      Slash Effect_4: 5379926381550770200
      Slash Effect_5: -4186273098142486107
      Slash Effect_6: -6598097557103341705
      Slash Effect_7: 4475434940280343158
      Slash Effect_8: 3335157376098573144
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
