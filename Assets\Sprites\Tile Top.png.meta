fileFormatVersion: 2
guid: ad735b6da1e13134fb47289e2a5f0547
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -5887488929226514375
    second: Tile Top_0
  - first:
      213: 6894716567044756874
    second: Tile Top_1
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 16
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Tile Top_0
      rect:
        serializedVersion: 2
        x: 0
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 930a86a13007b4ea0800000000000000
      internalID: -5887488929226514375
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tile Top_1
      rect:
        serializedVersion: 2
        x: 16
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a8514bd1034feaf50800000000000000
      internalID: 6894716567044756874
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tile Top_2
      rect:
        serializedVersion: 2
        x: 32
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cad371c40d368934fa90cfc7bd9fed2c
      internalID: 489497836
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tile Top_3
      rect:
        serializedVersion: 2
        x: 64
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d93b599519930fc4ba52b4da6843f18b
      internalID: 545852417
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tile Top_4
      rect:
        serializedVersion: 2
        x: 80
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8c17703a6e58d644cbff195e546b4869
      internalID: -2080103161
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tile Top_5
      rect:
        serializedVersion: 2
        x: 96
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cf457c3528ab36a4d9d6da5aaec2a83c
      internalID: -1861776891
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tile Top_6
      rect:
        serializedVersion: 2
        x: 0
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 48a973488ab89e447a379e1b12cb0342
      internalID: 1742242286
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tile Top_7
      rect:
        serializedVersion: 2
        x: 16
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a150138f64e95044382edd96852b9831
      internalID: 681691907
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tile Top_8
      rect:
        serializedVersion: 2
        x: 32
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d4009cb05f1283849b285e7f6f3dedee
      internalID: -2040611033
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tile Top_9
      rect:
        serializedVersion: 2
        x: 64
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 301ff23616971604fb6c8fb94160aa50
      internalID: 228425065
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tile Top_10
      rect:
        serializedVersion: 2
        x: 96
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2b652e02e1077404786ec6e1982b7356
      internalID: -2146227026
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tile Top_11
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bab9ff17164fa1f439067f7f63207465
      internalID: -433139049
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tile Top_12
      rect:
        serializedVersion: 2
        x: 16
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2c2114f4915939045a53329001c5b07e
      internalID: -1026862902
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tile Top_13
      rect:
        serializedVersion: 2
        x: 32
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1d248a577bc5c134fb687a48bd08ccec
      internalID: 1202349858
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tile Top_14
      rect:
        serializedVersion: 2
        x: 64
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 19221be3515378347aebc176abe6e9fd
      internalID: -555207047
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tile Top_15
      rect:
        serializedVersion: 2
        x: 80
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a98cef3110a571e488c68408a4a993ab
      internalID: -1144966791
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tile Top_16
      rect:
        serializedVersion: 2
        x: 96
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 560881b0589baf04eb811875551d51bc
      internalID: -1454017225
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: bec16ba53c269244f956f8eed7ace4ac
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Tile Top_0: -5887488929226514375
      Tile Top_1: 6894716567044756874
      Tile Top_10: -2146227026
      Tile Top_11: -433139049
      Tile Top_12: -1026862902
      Tile Top_13: 1202349858
      Tile Top_14: -555207047
      Tile Top_15: -1144966791
      Tile Top_16: -1454017225
      Tile Top_2: 489497836
      Tile Top_3: 545852417
      Tile Top_4: -2080103161
      Tile Top_5: -1861776891
      Tile Top_6: 1742242286
      Tile Top_7: 681691907
      Tile Top_8: -2040611033
      Tile Top_9: 228425065
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
