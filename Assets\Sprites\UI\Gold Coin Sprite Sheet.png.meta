fileFormatVersion: 2
guid: dbafa35f22a010940b645ff6ea6a4a6b
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -234688072440724827
    second: Gold Coin Sprite Sheet_0
  - first:
      213: -2773521449510625339
    second: Gold Coin Sprite Sheet_1
  - first:
      213: 3507830584120488930
    second: Gold Coin Sprite Sheet_2
  - first:
      213: -3027942650856943503
    second: Gold Coin Sprite Sheet_3
  - first:
      213: -8681122823199863251
    second: Gold Coin Sprite Sheet_4
  - first:
      213: 8605766828651519105
    second: Gold Coin Sprite Sheet_5
  - first:
      213: 4929703270696266693
    second: Gold Coin Sprite Sheet_6
  - first:
      213: -1796587115917803789
    second: Gold Coin Sprite Sheet_7
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 16
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Gold Coin Sprite Sheet_0
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5a2462901783ebcf0800000000000000
      internalID: -234688072440724827
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Gold Coin Sprite Sheet_1
      rect:
        serializedVersion: 2
        x: 16
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5cf1ef8bfe87289d0800000000000000
      internalID: -2773521449510625339
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Gold Coin Sprite Sheet_2
      rect:
        serializedVersion: 2
        x: 32
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2e3b0cab4515ea030800000000000000
      internalID: 3507830584120488930
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Gold Coin Sprite Sheet_3
      rect:
        serializedVersion: 2
        x: 48
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 170da67a3369af5d0800000000000000
      internalID: -3027942650856943503
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Gold Coin Sprite Sheet_4
      rect:
        serializedVersion: 2
        x: 64
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d2ec2af69c4768780800000000000000
      internalID: -8681122823199863251
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Gold Coin Sprite Sheet_5
      rect:
        serializedVersion: 2
        x: 80
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 18c1d99d553dd6770800000000000000
      internalID: 8605766828651519105
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Gold Coin Sprite Sheet_6
      rect:
        serializedVersion: 2
        x: 96
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5cbaee98103d96440800000000000000
      internalID: 4929703270696266693
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Gold Coin Sprite Sheet_7
      rect:
        serializedVersion: 2
        x: 112
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3fefcab698d3117e0800000000000000
      internalID: -1796587115917803789
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 0a458ec4c5c9f74448fc2a9c1fe49c87
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Gold Coin Sprite Sheet_0: -234688072440724827
      Gold Coin Sprite Sheet_1: -2773521449510625339
      Gold Coin Sprite Sheet_2: 3507830584120488930
      Gold Coin Sprite Sheet_3: -3027942650856943503
      Gold Coin Sprite Sheet_4: -8681122823199863251
      Gold Coin Sprite Sheet_5: 8605766828651519105
      Gold Coin Sprite Sheet_6: 4929703270696266693
      Gold Coin Sprite Sheet_7: -1796587115917803789
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
